#!/bin/bash

# AIOps智能故障诊断系统 Docker部署脚本
set -e

# 配置变量
# DATA_PATH根据实际在主机上的进行调整
IMAGE_NAME="aiops-diagnosis"
CONTAINER_NAME="aiops-diagnosis-container"
DATA_PATH="/data/data"

# 构建镜像
build() {
    echo "构建Docker镜像..."
    docker build -t "$IMAGE_NAME" .
    echo "镜像构建完成"
}

# 启动容器
run() {
    echo "启动容器..."

    # 停止现有容器
    docker stop "$CONTAINER_NAME" 2>/dev/null || true
    docker rm "$CONTAINER_NAME" 2>/dev/null || true

    touch answer.jsonl

    # 启动容器
    docker run -d \
        --name "$CONTAINER_NAME" \
        -v "$DATA_PATH:/data/data" \
        -v "$PWD/answer.jsonl:/app/results.jsonl" \
        "$IMAGE_NAME"

    echo "容器启动完成"
}

# 执行命令
case "$1" in
    "build")
        build
        ;;
    "run")
        run
        ;;
    *)
        echo "用法: $0 [build|run]"
        echo "  build    构建Docker镜像"
        echo "  run      启动容器"
        ;;
esac