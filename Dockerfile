# AIOps智能故障诊断系统 Docker镜像
# 基于Python 3.12构建，支持AutoGen框架和异步处理

# 使用Python 3.12 slim版本作为基础镜像（更小的体积）
FROM python:3.12-slim

# 设置维护者信息
LABEL maintainer="汪汪队"
LABEL description="AIOps_autogen"
LABEL version="1.0.0"

# 设置环境变量
ENV PYTHONUNBUFFERED=1 \
    PYTHONDONTWRITEBYTECODE=1 \
    PIP_NO_CACHE_DIR=1 \
    PIP_DISABLE_PIP_VERSION_CHECK=1

# 安装系统依赖
RUN apt-get update && apt-get install -y \
    gcc \
    g++ \
    make \
    curl \
    && rm -rf /var/lib/apt/lists/* \
    && apt-get clean

# 设置工作目录
WORKDIR /app

# 先复制requirements.txt并安装Python依赖
COPY src/requirements.txt /tmp/requirements.txt
RUN pip install --upgrade pip && \
    pip install -r /tmp/requirements.txt

# 复制源代码到容器
COPY src/ /app/

# 设置启动命令
CMD ["python3", "aiops_diagnosis/a2a_batch_diagnosis.py", "--input", "input.json", "--output", "results.jsonl", "--f", "jsonl", "--max-concurrent", "1"]