# 数据存放路径为： /data/data
# llm配置在 /src/aiops_diagnosis/config/llm_config.py

## 源代码启动方式
### 确保python 版本大于3.12 数据文件放在/data/data下  输出为results.jsonl
1  cd src
2. python3 -m venv venv
3. source venv/bin/activate
4. pip install -r requirements.txt
5. python3 aiops_diagnosis/a2a_batch_diagnosis.py --input input.json --output results.jsonl --f jsonl --max-concurrent 1

## Docker 启动方式
## 在run.sh 中修改宿主机数据存放位置 数据文件需要是解压后的  /data/data/每个日期文件夹
chmod +x run.sh
./run.sh build
./run.sh run

