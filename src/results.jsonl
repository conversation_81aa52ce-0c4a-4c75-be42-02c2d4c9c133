{"uuid": "345fbe93-80", "component": "emailservice", "reason": "pod processes abnormal", "time": "2025-06-05 16:11:00", "reasoning_trace": [{"step": 1, "action": "CheckPriority0Rules", "observation": "All emailservice pods show process count increase to 10"}, {"step": 2, "action": "AnalyzeMetrics(emailservice)", "observation": "Pod_processes_avg deviation 10x in all 3 pods"}, {"step": 3, "action": "TraceAnalysis(checkout->email)", "observation": "EmailService delays in checkout traces"}]}