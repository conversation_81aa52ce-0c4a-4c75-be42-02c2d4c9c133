#!/usr/bin/env python3
"""
A2A AIOps系统批量诊断工具 - 改进版
支持并发处理、流式输出、完整LLM结果保存
每个案例使用独立的Agent系统实例，确保完全隔离
"""

import asyncio
import json
import logging
import sys
import os
import argparse
import threading
from datetime import datetime
from pathlib import Path
from typing import Dict, Any, List, Optional
from dataclasses import dataclass
from concurrent.futures import ThreadPoolExecutor

# 添加项目根路径 - 修复：现在文件在aiops_diagnosis目录内，需要添加上级目录
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# 配置日志 - 过滤A2A消息噪音，只保留故障检测相关输出
def setup_clean_logging():
    """设置清洁的日志输出，只显示关键信息"""

    # 基础日志配置 - 改为INFO级别，减少debug输出
    logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')

    # 🔇 静默autogen_core框架日志（A2A消息传递）
    logging.getLogger("autogen_core").setLevel(logging.ERROR)
    logging.getLogger("autogen_core.events").setLevel(logging.ERROR)
    logging.getLogger("autogen_core.trace").setLevel(logging.ERROR)

    # 🔇 静默HTTP请求日志
    logging.getLogger("httpx").setLevel(logging.ERROR)
    logging.getLogger("httpcore").setLevel(logging.ERROR)

    # 🔇 静默数据加载的详细日志
    logging.getLogger("AutoGenDataLoaderAgent").setLevel(logging.ERROR)

    # 🔇 减少故障检测Agent的详细日志，只保留关键信息
    logging.getLogger("AutoGenMetricAnalysisAgent").setLevel(logging.WARNING)
    # 🔧 修复：启用SimpleMetricAgent的INFO日志，以便查看基线检测过程
    logging.getLogger("SimpleMetricAgent").setLevel(logging.INFO)
    logging.getLogger("APMServiceBaselineManager").setLevel(logging.INFO)
    logging.getLogger("ThresholdStrategyManager").setLevel(logging.INFO)
    logging.getLogger("AutoGenTraceAnalysisAgent").setLevel(logging.WARNING)
    logging.getLogger("EnhancedTraceAnalysisAgent").setLevel(logging.INFO)
    logging.getLogger("AutoGenLogAnalysisAgent").setLevel(logging.INFO)
    logging.getLogger("aiops_diagnosis.autogen_agents.new_reasoning_agent").setLevel(logging.INFO)

    # 🔇 减少系统级别的详细日志
    # 🔧 修复：启用证据构建日志以调试基线异常传递问题
    logging.getLogger("aiops_diagnosis.optimized_autogen_system").setLevel(logging.INFO)
    logging.getLogger("aiops_diagnosis.utils.file_path_locator").setLevel(logging.ERROR)

# 应用日志配置
setup_clean_logging()
logger = logging.getLogger(__name__)

@dataclass
class ProcessingConfig:
    """处理配置"""
    max_concurrent: int = 10  # 最大并发数
    enable_streaming: bool = True  # 启用流式输出
    preserve_llm_results: bool = True  # 保留完整LLM结果
    output_file: str = "output.json"  # 输出文件路径
    output_format: str = "json"  # 输出格式: "json" 或 "jsonl"
    dual_output: bool = False  # 同时输出两种格式
    performance_mode: str = "balanced"  # 性能模式
    config_file: Optional[str] = None  # 自定义配置文件

class StreamingOutputWriter:
    """流式输出写入器 - 线程安全，支持JSON和JSONL格式"""

    def __init__(self, output_file: str, output_format: str = "json"):
        self.output_file = Path(output_file)
        self.output_format = output_format.lower()  # "json" 或 "jsonl"
        self.lock = threading.Lock()
        self.results = []
        self.file_initialized = False

        # 根据格式调整文件扩展名
        if self.output_format == "jsonl" and not str(self.output_file).endswith('.jsonl'):
            self.output_file = self.output_file.with_suffix('.jsonl')

    def write_result(self, result: Dict[str, Any]):
        """写入单个结果"""
        with self.lock:
            self.results.append(result)
            self._write_to_file()

            # 🔧 新增：同时输出JSON和JSONL格式（如果需要）
            if hasattr(self, '_dual_output') and self._dual_output:
                self._write_dual_format(result)

    def _write_to_file(self):
        """原子性写入文件，支持JSON和JSONL格式"""
        try:
            # 写入临时文件，然后原子性重命名
            temp_file = self.output_file.with_suffix('.tmp')

            with open(temp_file, 'w', encoding='utf-8') as f:
                if self.output_format == "jsonl":
                    # JSONL格式：每行一个JSON对象
                    for result in self.results:
                        # 🔧 改进：直接使用原始结果，不进行额外转换
                        # 确保所有必要字段都存在
                        jsonl_item = self._ensure_jsonl_fields(result)
                        f.write(json.dumps(jsonl_item, ensure_ascii=False) + '\n')
                else:
                    # 默认JSON格式：JSON数组
                    json.dump(self.results, f, indent=2, ensure_ascii=False)

            # 原子性重命名
            temp_file.replace(self.output_file)
            format_info = f"({self.output_format.upper()}格式)" if self.output_format == "jsonl" else ""
            logger.info(f"✅ 已保存 {len(self.results)} 个结果到 {self.output_file} {format_info}")

        except Exception as e:
            logger.error(f"❌ 写入输出文件失败: {e}")

    def _write_dual_format(self, result: Dict[str, Any]):
        """同时输出JSON和JSONL格式的辅助方法"""
        try:
            # 生成对应的另一种格式文件名
            if self.output_format == "json":
                jsonl_file = self.output_file.with_suffix('.jsonl')
                # 追加写入JSONL格式
                with open(jsonl_file, 'a', encoding='utf-8') as f:
                    jsonl_item = self._ensure_jsonl_fields(result)
                    f.write(json.dumps(jsonl_item, ensure_ascii=False) + '\n')
            else:
                # 如果主格式是JSONL，同时维护JSON格式
                json_file = self.output_file.with_suffix('.json')
                with open(json_file, 'w', encoding='utf-8') as f:
                    json.dump(self.results, f, indent=2, ensure_ascii=False)

        except Exception as e:
            logger.warning(f"⚠️ 双格式输出失败: {e}")

    def _ensure_jsonl_fields(self, result: Dict[str, Any]) -> Dict[str, Any]:
        """确保JSONL格式包含所有必要字段，不进行过度转换"""
        # 🔧 改进：保持原始数据完整性，只确保必要字段存在
        jsonl_item = result.copy()  # 保留所有原始字段

        # 确保必要字段存在，如果缺失则提供默认值
        required_fields = {
            "uuid": result.get("uuid", ""),
            "component": result.get("component", ""),
            "reason": result.get("reason", ""),
            "reasoning_trace": result.get("reasoning_trace", [])
        }

        # 只更新缺失的字段
        for field, default_value in required_fields.items():
            if field not in jsonl_item or jsonl_item[field] is None:
                jsonl_item[field] = default_value

        return jsonl_item

    def _convert_to_jsonl_format(self, result: Dict[str, Any]) -> Dict[str, Any]:
        """将结果转换为JSONL标准格式 - 已弃用，使用_ensure_jsonl_fields代替"""
        # 🔧 保持向后兼容，但推荐使用新方法
        return self._ensure_jsonl_fields(result)

    def _standardize_component_name(self, component: str) -> str:
        """标准化组件名称，确保符合AIOps规则要求"""
        if not component or component == "unknown":
            return ""

        # 使用全局组件管理器进行动态验证和标准化
        try:
            from aiops_diagnosis.knowledge.global_component_manager import get_global_component_manager

            manager = get_global_component_manager()
            normalized = manager.normalize_component_name(component)

            if normalized:
                return normalized

            # 如果无法标准化，返回原始组件名
            return component

        except Exception as e:
            logger.warning(f"⚠️ 组件标准化失败: {e}")
            return component

    def enable_dual_output(self):
        """启用双格式输出（同时输出JSON和JSONL）"""
        self._dual_output = True
        logger.info(f"✅ 已启用双格式输出：{self.output_file} + 对应的另一种格式")

    def get_results(self) -> List[Dict[str, Any]]:
        """获取所有结果"""
        with self.lock:
            return self.results.copy()

def extract_pure_llm_result(llm_result: Dict[str, Any]) -> Dict[str, Any]:
    """提取纯净的LLM结果，符合规则说明.md格式"""
    try:
        # 🔧 修复：diagnosis_result本身就包含LLM的输出，不需要data字段
        # 检查是否包含必要的LLM字段
        required_fields = ['component', 'reason', 'reasoning_trace']
        missing_fields = [field for field in required_fields if field not in llm_result]

        if missing_fields:
            error_msg = f"❌ LLM结果缺少必要字段: {missing_fields}"
            print(error_msg)
            raise ValueError(error_msg)

        # 直接使用llm_result作为数据源
        data_source = llm_result
        print(f"   ✅ 使用LLM诊断结果（包含字段: {list(llm_result.keys())}）")

        # 提取LLM的原始输出字段
        result = {
            "uuid": data_source.get("uuid", llm_result.get("case_uuid", "unknown")),
            "component": data_source.get("component"),
            "reason": data_source.get("reason"),
            "time": data_source.get("time", "unknown"),
            "reasoning_trace": data_source.get("reasoning_trace", [])
        }

        # 最终验证和调试输出
        # print(f"   最终提取结果:")
        # print(f"     uuid: {result['uuid']}")
        # print(f"     component: {result['component']}")
        # print(f"     reason: {result['reason']}")
        # print(f"     time: {result['time']}")
        # print(f"     reasoning_trace: {len(result['reasoning_trace'])}个步骤")

        # print(f"   提取结果: component={result['component']}, reason={result['reason']}")
        # print(f"   推理步骤数: {len(result['reasoning_trace'])}")

        # 验证reasoning_trace格式
        if not isinstance(result["reasoning_trace"], list):
            logger.warning(f"reasoning_trace格式错误: {type(result['reasoning_trace'])}")
            result["reasoning_trace"] = []

        # 验证每个步骤的格式
        valid_trace = []
        for step in result["reasoning_trace"]:
            if isinstance(step, dict) and all(key in step for key in ["step", "action", "observation"]):
                valid_trace.append(step)
            else:
                logger.warning(f"无效的推理步骤: {step}")

        result["reasoning_trace"] = valid_trace

        logger.info(f"✅ 提取LLM结果: {result['component']} - {result['reason']}")
        logger.info(f"   推理步骤: {len(result['reasoning_trace'])}个")

        return result

    except Exception as e:
        logger.error(f"❌ LLM结果提取失败: {e}")
        # 返回基本格式
        return {
            "uuid": llm_result.get("case_uuid", "unknown"),
            "component": "unknown",
            "reason": "LLM结果提取失败",
            "time": datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
            "reasoning_trace": [
                {
                    "step": 1,
                    "action": "ErrorHandling",
                    "observation": f"提取失败: {str(e)[:20]}"
                }
            ]
        }

async def process_single_case_isolated(case_data: Dict[str, Any], case_index: int,
                                     config: ProcessingConfig,
                                     output_writer: StreamingOutputWriter) -> tuple[Dict[str, Any], float]:
    """处理单个案例 - 使用独立的Agent系统实例"""
    case_uuid = case_data.get("case_uuid") or case_data.get("uuid", f"case_{case_index+1}")

    # 记录案例处理开始时间
    case_start_time = datetime.now()

    try:
        logger.info(f"🚀 开始处理案例 {case_index+1}: {case_uuid}")

        # 为每个案例创建独立的系统实例
        from aiops_diagnosis.optimized_autogen_system import OptimizedAutoGenAIOpsSystem
        system = OptimizedAutoGenAIOpsSystem(
            # data_root_path="/data/data",
            data_root_path="/data/data",
            performance_mode=config.performance_mode
        )

        try:
            # 初始化独立系统
            await system.initialize()
            logger.info(f"✅ 案例 {case_uuid} 的独立系统初始化完成")

            # 提取时间信息
            start_time = case_data.get("start_time")
            end_time = case_data.get("end_time")

            # 如果没有直接的时间字段，尝试从描述中解析
            if not start_time or not end_time:
                description = case_data.get("Anomaly Description", "")
                times = extract_times_from_description(description)
                if times:
                    start_time, end_time = times

            if not start_time or not end_time:
                raise ValueError(f"无法获取案例 {case_uuid} 的时间信息")

            # 执行诊断
            logger.info(f"🔍 执行案例 {case_uuid} 的诊断分析...")
            diagnosis_result = await system.diagnose(
                case_uuid=case_uuid,
                start_time=start_time,
                end_time=end_time
            )

            # 🔧 调试：检查diagnosis_result的结构
            print(f"🔍 diagnosis_result类型: {type(diagnosis_result)}")
            print(f"🔍 diagnosis_result内容: {diagnosis_result}")

            # 提取纯净的LLM结果
            if config.preserve_llm_results:
                pure_result = extract_pure_llm_result(diagnosis_result)
            else:
                # 如果不保留LLM结果，使用传统格式转换
                pure_result = convert_llm_result_to_compatible_format(diagnosis_result, case_data)

            # 计算案例处理时间
            case_end_time = datetime.now()
            case_duration = (case_end_time - case_start_time).total_seconds()

            logger.info(f"✅ 案例 {case_uuid} 诊断完成: {pure_result.get('component', 'unknown')}")
            logger.info(f"⏱️ 案例处理耗时: {case_duration:.2f}秒")

            # 流式输出
            if config.enable_streaming:
                output_writer.write_result(pure_result)

            return pure_result, case_duration

        finally:
            # 🔧 增强资源清理 - 确保所有异步任务被正确清理
            try:
                if hasattr(system, 'shutdown'):
                    await system.shutdown()

                # 额外清理：等待所有pending任务完成
                if hasattr(system, 'runtime') and system.runtime:
                    try:
                        # 尝试优雅关闭runtime
                        if hasattr(system.runtime, 'stop_when_idle'):
                            await asyncio.wait_for(system.runtime.stop_when_idle(), timeout=5.0)
                        elif hasattr(system.runtime, 'stop'):
                            await asyncio.wait_for(system.runtime.stop(), timeout=5.0)
                    except asyncio.TimeoutError:
                        logger.warning(f"⚠️ 案例 {case_uuid} runtime关闭超时，强制清理")
                    except Exception as e:
                        logger.warning(f"⚠️ 案例 {case_uuid} runtime清理异常: {e}")

                # 清理可能的pending任务
                await asyncio.sleep(0.1)  # 给其他任务一个完成的机会

                logger.info(f"🧹 案例 {case_uuid} 的系统资源已完全清理")

            except Exception as cleanup_error:
                logger.error(f"❌ 案例 {case_uuid} 资源清理失败: {cleanup_error}")

    except Exception as e:
        # 计算失败案例的处理时间
        case_end_time = datetime.now()
        case_duration = (case_end_time - case_start_time).total_seconds()

        logger.error(f"❌ 案例 {case_uuid} 处理失败: {e}")
        logger.info(f"⏱️ 失败案例处理耗时: {case_duration:.2f}秒")

        # 创建错误结果
        error_result = {
            "uuid": case_uuid,
            "component": "error",
            "reason": f"处理失败: {str(e)[:50]}",
            "time": datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
            "reasoning_trace": [
                {
                    "step": 1,
                    "action": "ErrorHandling",
                    "observation": "案例处理异常"
                }
            ]
        }

        # 流式输出错误结果
        if config.enable_streaming:
            output_writer.write_result(error_result)

        return error_result, case_duration

async def process_cases_concurrently(input_data: List[Dict[str, Any]],
                                   config: ProcessingConfig) -> tuple[List[Dict[str, Any]], Dict[str, Any]]:
    """并发处理多个案例，返回结果和统计信息"""
    logger.info(f"🚀 开始并发处理 {len(input_data)} 个案例")
    logger.info(f"📊 并发配置: 最大并发数={config.max_concurrent}, 流式输出={config.enable_streaming}")

    # 创建流式输出写入器
    output_writer = StreamingOutputWriter(config.output_file, config.output_format)

    # 🔧 新增：启用双格式输出（如果需要）
    if config.dual_output:
        output_writer.enable_dual_output()

    # 创建信号量控制并发数
    semaphore = asyncio.Semaphore(config.max_concurrent)

    # 用于收集每个案例的处理时间
    case_durations = []

    async def process_with_semaphore(case_data: Dict[str, Any], case_index: int):
        """带信号量控制的案例处理"""
        async with semaphore:
            result, duration = await process_single_case_isolated(
                case_data, case_index, config, output_writer
            )
            case_durations.append(duration)
            return result

    # 创建所有任务
    tasks = [
        process_with_semaphore(case_data, i)
        for i, case_data in enumerate(input_data)
    ]

    # 并发执行所有任务
    logger.info(f"⚡ 启动 {len(tasks)} 个并发任务...")
    start_time = datetime.now()

    try:
        results = await asyncio.gather(*tasks, return_exceptions=True)

        # 处理异常结果
        processed_results = []
        for i, result in enumerate(results):
            if isinstance(result, Exception):
                logger.error(f"❌ 任务 {i+1} 异常: {result}")
                error_result = {
                    "uuid": f"case_{i+1}",
                    "component": "error",
                    "reason": f"任务异常: {str(result)[:50]}",
                    "time": datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
                    "reasoning_trace": [
                        {
                            "step": 1,
                            "action": "ExceptionHandling",
                            "observation": "任务执行异常"
                        }
                    ]
                }
                processed_results.append(error_result)
            else:
                processed_results.append(result)

        end_time = datetime.now()
        duration = (end_time - start_time).total_seconds()

        # 计算统计信息
        avg_case_duration = sum(case_durations) / len(case_durations) if case_durations else 0
        max_case_duration = max(case_durations) if case_durations else 0
        min_case_duration = min(case_durations) if case_durations else 0

        stats = {
            'total_duration': duration,
            'avg_case_duration': avg_case_duration,
            'max_case_duration': max_case_duration,
            'min_case_duration': min_case_duration,
            'case_durations': case_durations,
            'concurrent_efficiency': len(input_data) / duration if duration > 0 else 0,
            'time_saved': (avg_case_duration * len(input_data)) - duration if avg_case_duration > 0 else 0
        }

        logger.info(f"✅ 并发处理完成!")
        logger.info(f"📊 处理统计:")
        logger.info(f"   总案例数: {len(input_data)}")
        logger.info(f"   成功案例: {len([r for r in processed_results if r.get('component') != 'error'])}")
        logger.info(f"   失败案例: {len([r for r in processed_results if r.get('component') == 'error'])}")
        logger.info(f"   并发处理时间: {duration:.2f}秒")
        logger.info(f"   平均单案例时间: {avg_case_duration:.2f}秒")
        logger.info(f"   并发效率: {stats['concurrent_efficiency']:.2f}案例/秒")

        # 如果没有启用流式输出，最后统一写入
        if not config.enable_streaming:
            for result in processed_results:
                output_writer.write_result(result)

        return processed_results, stats

    except Exception as e:
        logger.error(f"❌ 并发处理失败: {e}")
        raise

# 保留原有的辅助函数
def extract_times_from_description(description: str):
    """从描述中提取时间信息"""
    import re

    # 匹配时间模式
    time_pattern = r'(\d{4}-\d{2}-\d{2}T\d{2}:\d{2}:\d{2}Z?)'
    matches = re.findall(time_pattern, description)

    if len(matches) >= 2:
        return matches[0], matches[1]
    return None

def convert_llm_result_to_compatible_format(llm_result: Dict[str, Any], case_data: Dict[str, Any]) -> Dict[str, Any]:
    """转换LLM结果为兼容格式 - 保留用于回退"""
    # 这个函数保留用于非LLM结果的回退处理
    return {
        "uuid": llm_result.get("case_uuid", "unknown"),
        "component": llm_result.get("component", "unknown"),
        "reason": "fallback_processing",
        "time": datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
        "reasoning_trace": [
            {
                "step": 1,
                "action": "FallbackProcessing",
                "observation": "使用回退处理逻辑"
            }
        ]
    }

async def main():
    """主函数"""
    parser = argparse.ArgumentParser(description="A2A AIOps系统批量诊断工具 - 改进版")
    parser.add_argument("-i", "--input", required=True, help="输入JSON文件路径")
    parser.add_argument("-o", "--output", required=True, help="输出文件路径")
    parser.add_argument("--f", choices=["json", "jsonl"], default="json",
                       help="输出格式 (默认: json, 可选: jsonl)")
    parser.add_argument("--dual-output", action="store_true",
                       help="同时输出JSON和JSONL两种格式")
    parser.add_argument("--max-concurrent", type=int, default=1, help="最大并发数 (默认: 1, 推荐1-2避免资源竞争)")
    parser.add_argument("--disable-streaming", action="store_true", help="禁用流式输出")
    parser.add_argument("--disable-llm-preservation", action="store_true", help="禁用LLM结果保留")
    parser.add_argument("--performance-mode", choices=["conservative", "balanced", "fast", "memory_optimized"],
                       default="balanced", help="性能模式 (默认: balanced)")
    parser.add_argument("--config-file", help="自定义配置文件路径")

    args = parser.parse_args()

    # 创建处理配置
    config = ProcessingConfig(
        max_concurrent=args.max_concurrent,
        enable_streaming=not args.disable_streaming,
        preserve_llm_results=not args.disable_llm_preservation,
        output_file=args.output,
        output_format=args.f,
        performance_mode=args.performance_mode,
        config_file=args.config_file
    )

    print("🚀 A2A AIOps系统批量诊断工具 - 改进版")
    print(f"📁 输入文件: {args.input}")
    print(f"📁 输出文件: {args.output}")
    print(f"📄 输出格式: {config.output_format.upper()}")
    print(f"⚡ 最大并发数: {config.max_concurrent}")
    print(f"🌊 流式输出: {'启用' if config.enable_streaming else '禁用'}")
    print(f"🤖 LLM结果保留: {'启用' if config.preserve_llm_results else '禁用'}")
    print(f"🚀 性能模式: {config.performance_mode}")
    if config.config_file:
        print(f"⚙️ 配置文件: {config.config_file}")
    print(f"⏰ 开始时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print("=" * 80)

    # 记录总体开始时间
    total_start_time = datetime.now()

    try:
        # 读取输入文件
        print(f"📋 Step 1: 读取输入文件 {args.input}...")
        file_read_start = datetime.now()
        with open(args.input, 'r', encoding='utf-8') as f:
            input_data = json.load(f)
        file_read_duration = (datetime.now() - file_read_start).total_seconds()

        if not isinstance(input_data, list):
            raise ValueError("输入文件必须包含案例数组")

        print(f"✅ 输入文件读取成功")
        print(f"   案例数量: {len(input_data)}")
        print(f"   读取耗时: {file_read_duration:.3f}秒")

        # 并发处理案例
        print(f"📋 Step 2: 开始并发诊断处理...")
        processing_start = datetime.now()
        results, stats = await process_cases_concurrently(input_data, config)
        processing_duration = (datetime.now() - processing_start).total_seconds()

        print(f"📋 Step 3: 处理完成统计...")

        # 计算总体时间
        total_end_time = datetime.now()
        total_duration = (total_end_time - total_start_time).total_seconds()

        # 统计结果
        success_count = len([r for r in results if r.get('component') != 'error'])
        error_count = len([r for r in results if r.get('component') == 'error'])

        print("✅ 批量诊断完成")
        print(f"   处理案例: {len(results)}")
        print(f"   输出文件: {args.output}")
        print()
        print("📊 诊断统计信息")
        print("=" * 50)
        print(f"   总案例数: {len(results)}")
        print(f"   成功案例: {success_count}")
        print(f"   失败案例: {error_count}")
        print(f"   成功率: {(success_count/len(results)*100):.1f}%")
        print()
        print("⏰ 时间统计")
        print("=" * 50)
        print(f"   开始时间: {total_start_time.strftime('%Y-%m-%d %H:%M:%S')}")
        print(f"   结束时间: {total_end_time.strftime('%Y-%m-%d %H:%M:%S')}")
        print(f"   总耗时: {total_duration:.2f}秒 ({total_duration/60:.1f}分钟)")
        print(f"   文件读取: {file_read_duration:.3f}秒")
        print(f"   诊断处理: {processing_duration:.2f}秒")
        print(f"   平均每案例: {stats['avg_case_duration']:.2f}秒")
        print(f"   最长案例: {stats['max_case_duration']:.2f}秒")
        print(f"   最短案例: {stats['min_case_duration']:.2f}秒")
        print(f"   并发效率: {stats['concurrent_efficiency']:.2f}案例/秒")
        if stats['time_saved'] > 0:
            print(f"   节省时间: {stats['time_saved']:.2f}秒 ({stats['time_saved']/60:.1f}分钟)")
            print(f"   并发加速比: {(stats['avg_case_duration'] * len(results)) / processing_duration:.1f}x")
        print()

        # 根因统计
        component_stats = {}
        for result in results:
            component = result.get('component', 'unknown')
            if component != 'error':
                component_stats[component] = component_stats.get(component, 0) + 1

        if component_stats:
            print("   检测到的根因:")
            for component, count in sorted(component_stats.items(), key=lambda x: x[1], reverse=True):
                print(f"     {component}: {count}个案例")

        print()
        print("🎉 批量诊断完成！")
        print(f"✅ 结果已保存到: {args.output}")

    except Exception as e:
        print(f"❌ 批量诊断失败: {e}")
        logger.error(f"批量诊断失败: {e}", exc_info=True)
        return 1

    return 0

if __name__ == "__main__":
    import sys
    sys.exit(asyncio.run(main()))
