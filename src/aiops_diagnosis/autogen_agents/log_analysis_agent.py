#!/usr/bin/env python3
"""
重构的日志分析智能体 - 解决误判正常日志为异常的问题
基于严格的编程逻辑识别真正的故障日志，避免将正常业务日志误判为异常

核心设计原则：
1. 基于严格的日志级别过滤（ERROR/FATAL/CRITICAL）
2. 基于明确的错误关键词匹配
3. 避免过度依赖LLM导致的误判
4. 保持与现有系统的接口兼容性
"""

import pandas as pd
import re
import json
import logging
import os
from typing import Dict, Any, List, Optional
from collections import Counter, defaultdict
from datetime import datetime
from autogen_core import MessageContext

from .base_aiops_agent import (
    BaseAIOpsAgent, AnalysisRequest, AnalysisResponse,
    EnhancedAnalysisResponse, SuspiciousEntity, DataLocation, TimeRange,
    EntityInfo, AnomalyFeature
)
from ..utils.file_path_locator import FilePathLocator
from ..knowledge.global_component_manager import get_global_component_manager
from ..config.config_manager import config_manager
from ..utils.concurrency_limiter import global_limiter


class AutoGenLogAnalysisAgent(BaseAIOpsAgent):
    """重构的日志分析智能体 - 基于严格规则识别真正的故障日志"""
    
    def __init__(self, enable_llm: bool = True, data_root_path: str = "/data/data"):
        super().__init__("Refactored log analysis agent for AIOps diagnosis", enable_llm)

        # 数据路径配置
        self.data_root_path = data_root_path
        self.data_root = data_root_path  # 添加data_root属性以保持兼容性
        self.file_locator = FilePathLocator(data_root_path)
        self.component_manager = get_global_component_manager()
        
        # 严格的错误检测规则
        self.error_levels = {
            'CRITICAL', 'FATAL', 'ERROR',  # 大写
            'critical', 'fatal', 'error',  # 小写
            'Critical', 'Fatal', 'Error'   # 首字母大写
        }
        
        # 明确的错误关键词（只匹配真正的错误）
        self.error_keywords = {
            'exception', 'Exception', 'EXCEPTION',
            'traceback', 'Traceback', 'TRACEBACK',
            'stack trace', 'Stack Trace', 'STACK TRACE',
            'out of memory', 'OutOfMemoryError', 'OOM',
            'connection refused', 'Connection refused',
            'connection timeout', 'Connection timeout',
            'deadline exceeded', 'Deadline exceeded',
            'service unavailable', 'Service unavailable',
            'internal server error', 'Internal Server Error',
            'panic', 'Panic', 'PANIC',
            'segmentation fault', 'Segmentation fault'
        }
        
        # 正常业务关键词（不应被标记为异常）
        self.normal_keywords = {
            'request started', 'request finished', 'request completed',
            'received request', 'processing request', 'handling request',
            'response sent', 'response completed', 'response successful',
            'conversion request successful', 'operation successful',
            'endpoint executed', 'method executed', 'function executed',
            'connection established', 'connection successful',
            'authentication successful', 'authorization successful'
        }
        
        self.logger = logging.getLogger(self.__class__.__name__)

    async def _handle_analysis_request(self, request: AnalysisRequest, ctx: MessageContext) -> AnalysisResponse:
        """处理分析请求 - BaseAIOpsAgent要求的抽象方法实现"""
        return await self.analyze(request, ctx)

    async def analyze(self, request: AnalysisRequest, ctx: MessageContext) -> AnalysisResponse:
        """主要分析入口 - 基于严格规则的日志异常检测"""
        try:
            self.logger.info(f"🔍 开始重构的日志分析: 案例 {request.case_uuid}")
            
            # 1. 加载日志数据
            logs_data = await self._load_logs_data(request.case_uuid, request.start_time, request.end_time)
            if logs_data.empty:
                self.logger.warning("⚠️ 未找到日志数据")
                return self._create_empty_response(request)
            
            self.logger.info(f"📊 加载日志数据: {len(logs_data)} 条记录")
            
            # 2. 基于严格规则的错误日志检测
            error_logs = self._detect_error_logs_strict(logs_data)
            self.logger.info(f"🚨 检测到真正的错误日志: {len(error_logs)} 条")
            
            # 3. 构建可疑实体（只包含真正有错误的服务）
            suspicious_entities = self._build_suspicious_entities(error_logs, request.case_uuid)
            
            # 4. 生成分析响应 - 🔧 修复接口兼容性
            results = {
                "analysis_summary": f"基于严格规则检测到 {len(error_logs)} 条真正的错误日志",
                "total_logs": len(logs_data),
                "error_logs_count": len(error_logs),
                "error_logs": error_logs[:10],
                "detection_method": "strict_rule_based",
                "confidence": 0.9 if error_logs else 0.1,
                "suspicious_entities": suspicious_entities,
                "standardized_output": {
                    "suspicious_entities": suspicious_entities,
                    "analysis_summary": f"基于严格规则检测到 {len(error_logs)} 条真正的错误日志"
                }
            }

            response = AnalysisResponse(
                case_uuid=request.case_uuid,
                start_time=request.start_time,
                end_time=request.end_time,
                data=request.data,
                analysis_type="logs",
                results=results,
                success=True
            )
            
            self.logger.info(f"✅ 日志分析完成: 发现 {len(suspicious_entities)} 个可疑服务")
            return response
            
        except Exception as e:
            self.logger.error(f"❌ 日志分析失败: {e}")
            return AnalysisResponse(
                case_uuid=request.case_uuid,
                start_time=request.start_time,
                end_time=request.end_time,
                data=request.data,
                analysis_type="logs",
                results={},
                success=False,
                error=str(e)
            )

    def _detect_error_logs_strict(self, logs_data: pd.DataFrame) -> List[Dict[str, Any]]:
        """基于严格规则检测错误日志 - 避免误判正常业务日志"""
        error_logs = []
        
        for _, log_entry in logs_data.iterrows():
            message = str(log_entry.get('message', ''))
            pod_name = log_entry.get('k8_pod', 'unknown')
            timestamp = log_entry.get('@timestamp', '')
            
            # 跳过空消息
            if not message or message.strip() == '':
                continue
            
            # 检查是否为正常业务日志（优先排除）
            if self._is_normal_business_log(message):
                continue
            
            # 检查是否为真正的错误日志
            error_type = self._classify_error_type(message)
            if error_type:
                service_name = self._extract_service_name_from_pod(pod_name)
                
                # 验证服务名称是否有效
                if not self.component_manager.is_valid_component(service_name):
                    continue
                
                error_logs.append({
                    'timestamp': timestamp,
                    'pod': pod_name,
                    'service': service_name,
                    'message': message[:500],  # 限制消息长度
                    'error_type': error_type,
                    'severity': self._extract_severity_from_message(message),
                    'source': 'strict_rule_detection'
                })
        
        return error_logs

    def _is_normal_business_log(self, message: str) -> bool:
        """判断是否为正常业务日志"""
        message_lower = message.lower()
        
        # 检查是否包含正常业务关键词
        for keyword in self.normal_keywords:
            if keyword.lower() in message_lower:
                return True
        
        # 检查JSON格式日志的severity字段
        if message.strip().startswith('{'):
            try:
                log_json = json.loads(message)
                severity = log_json.get('severity', log_json.get('level', '')).upper()
                
                # DEBUG和INFO级别的日志通常是正常业务日志
                if severity in ['DEBUG', 'INFO', 'TRACE']:
                    return True
                    
                # 检查消息内容是否为正常业务操作
                log_message = log_json.get('message', '').lower()
                for keyword in self.normal_keywords:
                    if keyword.lower() in log_message:
                        return True
                        
            except (json.JSONDecodeError, AttributeError):
                pass
        
        return False

    def _classify_error_type(self, message: str) -> Optional[str]:
        """分类错误类型 - 只识别真正的错误"""
        message_lower = message.lower()
        
        # 1. 检查错误级别关键词
        for level in self.error_levels:
            if level in message:
                return f"log_level_{level.lower()}"
        
        # 2. 检查JSON格式日志的severity字段
        if message.strip().startswith('{'):
            try:
                log_json = json.loads(message)
                severity = log_json.get('severity', log_json.get('level', '')).upper()
                
                if severity in ['ERROR', 'FATAL', 'CRITICAL']:
                    return f"json_severity_{severity.lower()}"
                    
            except (json.JSONDecodeError, AttributeError):
                pass
        
        # 3. 检查明确的错误关键词
        for keyword in self.error_keywords:
            if keyword.lower() in message_lower:
                return f"error_keyword_{keyword.lower().replace(' ', '_')}"
        
        # 4. 检查HTTP错误状态码
        http_error_pattern = r'(status|code|response)[:\s]*([45]\d{2})'
        match = re.search(http_error_pattern, message_lower)
        if match:
            status_code = match.group(2)
            return f"http_error_{status_code}"
        
        return None

    def _extract_severity_from_message(self, message: str) -> str:
        """从消息中提取严重程度"""
        # 检查JSON格式
        if message.strip().startswith('{'):
            try:
                log_json = json.loads(message)
                severity = log_json.get('severity', log_json.get('level', ''))
                if severity:
                    return severity.upper()
            except (json.JSONDecodeError, AttributeError):
                pass
        
        # 检查文本中的级别关键词
        message_upper = message.upper()
        for level in ['CRITICAL', 'FATAL', 'ERROR', 'WARN', 'INFO', 'DEBUG']:
            if level in message_upper:
                return level
        
        return 'UNKNOWN'

    def _extract_service_name_from_pod(self, pod_name: str) -> str:
        """从Pod名称提取服务名称"""
        if not pod_name or pod_name == 'unknown':
            return 'unknown'
        
        # 移除Pod后缀（如 -0, -1, -2）
        service_name = re.sub(r'-\d+$', '', pod_name)
        return service_name

    async def _load_logs_data(self, case_uuid: str, start_time: str, end_time: str) -> pd.DataFrame:
        """通过DataLoader加载日志数据 - 修复架构问题"""
        try:
            from autogen_core import AgentId
            from .base_aiops_agent import DataLoadRequest

            # 🔧 修复：正确使用DataLoader，参考TraceAgent的做法
            self.logger.info(f"🔍 重构的LogAgent从DataLoader获取logs数据")

            # 构建数据请求
            data_load_request = DataLoadRequest(
                case_uuid=case_uuid,
                start_time=start_time,
                end_time=end_time,
                data={}
            )

            # 获取DataLoader的Agent ID并发送请求
            data_loader_id = AgentId("DataLoader", "default")
            self.logger.info(f"📤 向DataLoader请求logs数据: {case_uuid}")

            response = await self.send_message(data_load_request, data_loader_id)

            if response and response.success and response.data:
                logs_data = response.data.get("logs")
                if logs_data is not None and not logs_data.empty:
                    self.logger.info(f"✅ 从DataLoader获取到 {len(logs_data)} 条logs记录")
                    return logs_data
                else:
                    self.logger.warning("⚠️ DataLoader返回空的logs数据")
                    return pd.DataFrame()
            else:
                self.logger.warning("⚠️ DataLoader请求失败或返回无效响应")
                return pd.DataFrame()

        except Exception as e:
            self.logger.error(f"❌ 通过DataLoader加载日志数据失败: {e}")
            return pd.DataFrame()

    def _filter_by_time_range(self, logs_data: pd.DataFrame, start_time: str, end_time: str) -> pd.DataFrame:
        """按时间范围过滤日志"""
        try:
            if '@timestamp' not in logs_data.columns:
                return logs_data
            
            # 转换时间格式
            logs_data['@timestamp'] = pd.to_datetime(logs_data['@timestamp'])
            start_dt = pd.to_datetime(start_time)
            end_dt = pd.to_datetime(end_time)
            
            # 过滤时间范围
            filtered_logs = logs_data[
                (logs_data['@timestamp'] >= start_dt) & 
                (logs_data['@timestamp'] <= end_dt)
            ]
            
            return filtered_logs
            
        except Exception as e:
            self.logger.error(f"❌ 时间过滤失败: {e}")
            return logs_data

    def _build_suspicious_entities(self, error_logs: List[Dict[str, Any]], case_uuid: str) -> List[SuspiciousEntity]:
        """构建可疑实体 - 只包含真正有错误的服务"""
        if not error_logs:
            return []
        
        # 按服务分组错误日志
        service_errors = defaultdict(list)
        for error in error_logs:
            service = error.get('service', 'unknown')
            if service != 'unknown':
                service_errors[service].append(error)
        
        suspicious_entities = []
        for service, errors in service_errors.items():
            # 验证组件名称
            if not self.component_manager.is_valid_component(service):
                continue
            
            # 统计错误类型
            error_types = Counter(error.get('error_type', 'unknown') for error in errors)
            severity_counts = Counter(error.get('severity', 'UNKNOWN') for error in errors)
            
            # 计算置信度（基于错误数量和严重程度）
            confidence = min(0.9, 0.5 + len(errors) * 0.1)
            if 'CRITICAL' in severity_counts or 'FATAL' in severity_counts:
                confidence = min(0.95, confidence + 0.2)
            
            suspicious_entity = SuspiciousEntity(
                entity=EntityInfo(
                    service=service,
                    namespace="hipstershop",
                    pod=errors[0].get('pod', 'unknown')
                ),
                time_range=TimeRange(start="", end=""),
                anomaly_feature=AnomalyFeature(
                    pattern=f"错误类型: {', '.join(error_types.keys())}",
                    confidence=confidence,
                    log_level="high" if any(s in severity_counts for s in ['CRITICAL', 'FATAL', 'ERROR']) else "medium",
                    key_log_fragment=f"发现 {len(errors)} 条真正的错误日志"
                ),
                data_location=DataLocation(
                    file_type="log",
                    root_dir="",
                    file_paths=[],  # 简化处理
                    file_matching_rule="strict_rule_based_detection"
                ),
                confidence=confidence,
                raw_data_records=errors[:5],  # 最多包含5条错误日志作为证据
                data_summary={
                    "total_errors": len(errors),
                    "error_types": dict(error_types),
                    "severity_distribution": dict(severity_counts),
                    "time_span": f"{errors[0].get('timestamp', '')} - {errors[-1].get('timestamp', '')}"
                }
            )
            
            suspicious_entities.append(suspicious_entity)
        
        return suspicious_entities

    def _get_log_file_paths(self, start_time: str, end_time: str) -> List[str]:
        """获取日志文件路径"""
        try:
            return self.file_locator.get_log_files(start_time, end_time)
        except Exception:
            return []

    def _create_empty_response(self, request: AnalysisRequest) -> AnalysisResponse:
        """创建空响应"""
        results = {
            "analysis_summary": "未检测到真正的错误日志",
            "total_logs": 0,
            "error_logs_count": 0,
            "detection_method": "strict_rule_based",
            "confidence": 0.1,
            "suspicious_entities": [],
            "standardized_output": {
                "suspicious_entities": [],
                "analysis_summary": "未检测到真正的错误日志"
            }
        }

        return AnalysisResponse(
            case_uuid=request.case_uuid,
            start_time=request.start_time,
            end_time=request.end_time,
            data=request.data,
            analysis_type="logs",
            results=results,
            success=True
        )

    def get_analysis_summary(self, error_logs: List[Dict[str, Any]]) -> str:
        """生成分析摘要"""
        if not error_logs:
            return "✅ 系统日志正常，未发现真正的错误日志"

        # 统计错误分布
        service_counts = Counter(log.get('service', 'unknown') for log in error_logs)
        error_type_counts = Counter(log.get('error_type', 'unknown') for log in error_logs)
        severity_counts = Counter(log.get('severity', 'UNKNOWN') for log in error_logs)

        summary_parts = [
            f"🚨 检测到 {len(error_logs)} 条真正的错误日志",
            f"📊 涉及服务: {', '.join(f'{service}({count})' for service, count in service_counts.most_common(3))}",
            f"🔍 主要错误类型: {', '.join(f'{etype}({count})' for etype, count in error_type_counts.most_common(3))}",
            f"⚠️ 严重程度分布: {', '.join(f'{severity}({count})' for severity, count in severity_counts.most_common())}"
        ]

        return " | ".join(summary_parts)

    def validate_error_log(self, message: str, pod_name: str) -> bool:
        """验证是否为真正的错误日志 - 最终验证"""
        # 1. 排除明确的正常业务日志
        if self._is_normal_business_log(message):
            return False

        # 2. 必须包含明确的错误指示
        error_type = self._classify_error_type(message)
        if not error_type:
            return False

        # 3. 验证服务名称有效性
        service_name = self._extract_service_name_from_pod(pod_name)
        if not self.component_manager.is_valid_component(service_name):
            return False

        # 4. 额外的误判防护
        message_lower = message.lower()

        # 排除一些常见的误判情况
        false_positive_patterns = [
            'request started',
            'request finished',
            'request completed',
            'response sent',
            'connection established',
            'authentication successful',
            'operation successful',
            'processing request',
            'handling request'
        ]

        for pattern in false_positive_patterns:
            if pattern in message_lower:
                return False

        return True

    def get_error_statistics(self, error_logs: List[Dict[str, Any]]) -> Dict[str, Any]:
        """获取错误统计信息"""
        if not error_logs:
            return {
                "total_errors": 0,
                "services_affected": 0,
                "error_types": {},
                "severity_distribution": {},
                "time_distribution": {}
            }

        # 服务统计
        services = set(log.get('service', 'unknown') for log in error_logs)

        # 错误类型统计
        error_types = Counter(log.get('error_type', 'unknown') for log in error_logs)

        # 严重程度统计
        severity_counts = Counter(log.get('severity', 'UNKNOWN') for log in error_logs)

        # 时间分布统计（按小时）
        time_distribution = defaultdict(int)
        for log in error_logs:
            timestamp = log.get('timestamp', '')
            if timestamp:
                try:
                    dt = pd.to_datetime(timestamp)
                    hour_key = dt.strftime('%H:00')
                    time_distribution[hour_key] += 1
                except Exception:
                    pass

        return {
            "total_errors": len(error_logs),
            "services_affected": len(services),
            "affected_services": list(services),
            "error_types": dict(error_types),
            "severity_distribution": dict(severity_counts),
            "time_distribution": dict(time_distribution),
            "detection_method": "strict_rule_based",
            "false_positive_prevention": True
        }
