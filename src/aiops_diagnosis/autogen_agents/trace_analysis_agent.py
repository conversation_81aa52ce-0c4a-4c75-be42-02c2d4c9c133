#!/usr/bin/env python3
"""
基于AutoGen的调用链分析智能体
"""

import pandas as pd
import numpy as np
from typing import Dict, Any, List, Optional
from collections import defaultdict, Counter
from autogen_core import MessageContext
from .base_aiops_agent import (
    BaseAIOpsAgent, AnalysisRequest, AnalysisResponse,
    EnhancedAnalysisResponse, SuspiciousEntity, DataLocation, TimeRange,
    EntityInfo, AnomalyFeature
)
from ..utils.file_path_locator import FilePathLocator
from ..knowledge.global_component_manager import get_global_component_manager
from ..knowledge.service_dependency_builder import ServiceDependencyBuilder
from ..config.config_manager import config_manager
from ..utils.concurrency_limiter import global_limiter
from ..utils.unit_normalizer import UnitNormalizer


class AutoGenTraceAnalysisAgent(BaseAIOpsAgent):
    """基于AutoGen的调用链分析智能体"""
    
    def __init__(self, enable_llm: bool = True, data_root_path: str = "/data/phaseone"):  # 🚨 重新启用LLM获取详细分析
        super().__init__("Trace analysis agent for AIOps diagnosis", enable_llm)

        # 初始化文件路径定位器
        self.file_locator = FilePathLocator(data_root_path)

        # 使用全局组件管理器
        self.component_manager = get_global_component_manager(data_root_path)

        # 获取并发配置
        self.concurrency_config = config_manager.config
        self.global_limiter = global_limiter

        # 初始化服务依赖关系构建器
        self.dependency_builder = ServiceDependencyBuilder()
        self.current_dependency_graph = None  # 保存当前的依赖图

        # 调用链异常阈值 - 更严格的设置
        self.trace_thresholds = {
            'duration': {'high': 3000, 'critical': 10000},  # ms - 提高到3秒和10秒
            'error_rate': {'high': 0.15, 'critical': 0.3},  # 提高到15%和30%
            'span_count': {'high': 100, 'critical': 200}    # 提高span数量阈值
        }
    


    async def _extract_error_evidence_from_tags_logs(self, traces_data: pd.DataFrame) -> List[Dict[str, Any]]:
        """从tags和logs字段中提取错误信息作为异常证据"""
        error_evidence = []

        try:
            self.logger.info("🔍 开始从tags和logs中提取错误证据...")

            for _, trace in traces_data.iterrows():
                service_name, pod_instance = self._extract_service_and_pod_from_trace(trace)

                # 分析tags字段中的错误信息
                tags_errors = self._analyze_tags_for_errors(trace, service_name, pod_instance)
                error_evidence.extend(tags_errors)

                # 分析logs字段中的错误信息
                logs_errors = self._analyze_logs_for_errors(trace, service_name, pod_instance)
                error_evidence.extend(logs_errors)

            # 去重和聚合相似的错误
            error_evidence = self._deduplicate_error_evidence(error_evidence)

            if error_evidence:
                self.logger.info(f"📊 从tags/logs中提取到 {len(error_evidence)} 个错误证据")
                for i, evidence in enumerate(error_evidence[:3], 1):
                    service = evidence.get('service', 'unknown')
                    error_type = evidence.get('error_type', 'unknown')
                    severity = evidence.get('severity', 'unknown')
                    self.logger.info(f"   {i}. {service} - {error_type} ({severity})")
            else:
                self.logger.info("✅ tags/logs中未发现明显错误信息")

        except Exception as e:
            self.logger.error(f"❌ 错误证据提取失败: {e}")

        return error_evidence

    async def _comprehensive_trace_fault_detection(self, traces_data: pd.DataFrame) -> Dict[str, Any]:
        """基于trace字段特点的综合故障检测 - 🎯 核心检测方法"""
        try:
            self.logger.info("🚀 启动基于trace字段的综合故障检测")

            detection_results = {
                "error_evidence": [],
                "duration_anomalies": [],
                "trace_chain_faults": [],
                "service_fault_summary": {},
                "confidence": 0.0
            }

            if traces_data.empty:
                self.logger.warning("⚠️ 无trace数据可用于故障检测")
                return detection_results

            # 1. 基于tags/logs的错误检测（使用现有方法）
            self.logger.info("🔍 第1步：基于tags/logs的错误检测")
            error_evidence = await self._extract_error_evidence_from_tags_logs(traces_data)
            detection_results["error_evidence"] = error_evidence

            # 2. 基于duration的智能阈值检测
            self.logger.info("🔍 第2步：基于duration的智能阈值检测")
            duration_anomalies = self._intelligent_duration_fault_detection(traces_data)
            detection_results["duration_anomalies"] = duration_anomalies

            # 3. 基于调用链关联的故障传播检测
            self.logger.info("🔍 第3步：基于调用链关联的故障传播检测")
            trace_chain_faults = self._trace_chain_fault_propagation_detection(traces_data)
            detection_results["trace_chain_faults"] = trace_chain_faults

            # 4. 聚合服务级别的故障总结
            self.logger.info("🔍 第4步：聚合服务级别的故障总结")
            service_fault_summary = self._aggregate_service_fault_summary(
                error_evidence, duration_anomalies, trace_chain_faults
            )
            detection_results["service_fault_summary"] = service_fault_summary

            # 5. 计算整体置信度
            total_faults = len(error_evidence) + len(duration_anomalies) + len(trace_chain_faults)
            detection_results["confidence"] = min(0.95, 0.3 + total_faults * 0.1)

            self.logger.info(f"✅ 综合故障检测完成:")
            self.logger.info(f"   - 错误证据: {len(error_evidence)}个")
            self.logger.info(f"   - Duration异常: {len(duration_anomalies)}个")
            self.logger.info(f"   - 调用链故障: {len(trace_chain_faults)}个")
            self.logger.info(f"   - 整体置信度: {detection_results['confidence']:.3f}")

            return detection_results

        except Exception as e:
            self.logger.error(f"❌ 综合故障检测失败: {e}")
            return {
                "error_evidence": [],
                "duration_anomalies": [],
                "trace_chain_faults": [],
                "service_fault_summary": {},
                "confidence": 0.0
            }

    def _analyze_tags_for_errors(self, trace, service_name: str, pod_instance: str) -> List[Dict[str, Any]]:
        """分析trace的tags字段中的错误信息"""
        errors = []

        try:
            tags_list = trace.get('tags', [])
            if not isinstance(tags_list, list):
                return errors

            # 检查gRPC状态码
            grpc_status = None
            error_flag = False
            span_kind = None

            for tag in tags_list:
                if not isinstance(tag, dict):
                    continue

                key = tag.get('key', '')
                value = tag.get('value', '')

                # 检查gRPC状态码
                if key == 'rpc.grpc.status_code':
                    try:
                        grpc_status = int(value)
                    except (ValueError, TypeError):
                        grpc_status = None

                # 检查错误标志
                elif key == 'error' and str(value).lower() in ['true', '1', 'yes']:
                    error_flag = True

                # 检查span类型
                elif key == 'span.kind':
                    span_kind = value

                # 检查HTTP状态码
                elif key == 'http.status_code':
                    try:
                        http_status = int(value)
                        if http_status >= 400:
                            errors.append({
                                "type": "http_error",
                                "service": service_name,
                                "pod_instance": pod_instance,
                                "error_type": "http_error",
                                "severity": "high" if http_status >= 500 else "medium",
                                "description": f"HTTP错误状态码: {http_status}",
                                "evidence": f"HTTP状态码 {http_status} 在 {service_name}",
                                "source": "tags_analysis"
                            })
                    except (ValueError, TypeError):
                        pass

            # 处理gRPC错误
            if grpc_status is not None and grpc_status != 0:
                severity = "critical" if grpc_status in [2, 3, 4, 14] else "high"
                errors.append({
                    "type": "grpc_error",
                    "service": service_name,
                    "pod_instance": pod_instance,
                    "error_type": "grpc_error",
                    "severity": severity,
                    "description": f"gRPC错误状态码: {grpc_status}",
                    "evidence": f"gRPC状态码 {grpc_status} 在 {service_name}",
                    "source": "tags_analysis"
                })

            # 处理通用错误标志
            if error_flag:
                errors.append({
                    "type": "span_error",
                    "service": service_name,
                    "pod_instance": pod_instance,
                    "error_type": "span_error",
                    "severity": "medium",
                    "description": f"Span标记为错误状态",
                    "evidence": f"错误标志在 {service_name} 的 {span_kind or 'unknown'} span",
                    "source": "tags_analysis"
                })

        except Exception as e:
            self.logger.warning(f"分析tags错误信息失败: {e}")

        return errors

    def _analyze_logs_for_errors(self, trace, service_name: str, pod_instance: str) -> List[Dict[str, Any]]:
        """分析trace的logs字段中的错误信息"""
        errors = []

        try:
            logs_list = trace.get('logs', [])
            if not isinstance(logs_list, list):
                return errors

            for log_entry in logs_list:
                if not isinstance(log_entry, dict):
                    continue

                # 检查日志字段
                fields = log_entry.get('fields', [])
                if not isinstance(fields, list):
                    continue

                error_found = False
                error_message = ""
                stack_trace = ""

                for field in fields:
                    if not isinstance(field, dict):
                        continue

                    key = field.get('key', '')
                    value = str(field.get('value', ''))

                    # 检查错误级别
                    if key == 'level' and value.upper() in ['ERROR', 'FATAL', 'CRITICAL']:
                        error_found = True

                    # 检查错误消息
                    elif key in ['message', 'error.message', 'exception.message']:
                        if any(keyword in value.lower() for keyword in ['error', 'exception', 'failed', 'timeout', 'refused']):
                            error_found = True
                            error_message = value

                    # 检查堆栈跟踪
                    elif key in ['stack', 'stacktrace', 'exception.stacktrace']:
                        error_found = True
                        stack_trace = value[:200]  # 限制长度

                if error_found:
                    errors.append({
                        "type": "log_error",
                        "service": service_name,
                        "pod_instance": pod_instance,
                        "error_type": "log_error",
                        "severity": "high",
                        "description": f"日志中发现错误: {error_message[:100] if error_message else '错误级别日志'}",
                        "evidence": f"错误日志在 {service_name}: {error_message or stack_trace}",
                        "source": "logs_analysis"
                    })

        except Exception as e:
            self.logger.warning(f"分析logs错误信息失败: {e}")

        return errors

    def _deduplicate_error_evidence(self, error_evidence: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """去重和聚合相似的错误证据"""
        if not error_evidence:
            return []

        # 按服务和错误类型分组
        grouped_errors = {}

        for error in error_evidence:
            key = f"{error.get('service', 'unknown')}_{error.get('error_type', 'unknown')}"
            if key not in grouped_errors:
                grouped_errors[key] = []
            grouped_errors[key].append(error)

        # 每组只保留一个代表性错误，但统计数量
        deduplicated = []
        for key, errors in grouped_errors.items():
            if not errors:
                continue

            # 选择最严重的错误作为代表
            representative = max(errors, key=lambda x: {'critical': 4, 'high': 3, 'medium': 2, 'low': 1}.get(x.get('severity', 'low'), 1))

            # 更新描述以包含数量信息
            if len(errors) > 1:
                representative['description'] += f" (共发现 {len(errors)} 次)"
                representative['evidence'] += f" (重复 {len(errors)} 次)"

            deduplicated.append(representative)

        return deduplicated

    def _is_span_anomalous_by_service_type(self, service_name: str, duration_ms: float, span) -> bool:
        """基于HipsterShop服务类型的智能异常检测 - 🔧 基于实际架构"""
        try:
            # 🎯 基于HipsterShop架构的服务分类和阈值
            service_lower = service_name.lower()

            # 1. Frontend服务：用户交互入口，阈值较宽松
            if 'frontend' in service_lower:
                return duration_ms > 2000  # 2秒

            # 2. 数据库/缓存服务：要求快速响应
            elif any(db in service_lower for db in ['redis', 'cart', 'tidb']):
                return duration_ms > 100   # 100毫秒

            # 3. 查询服务：中等阈值
            elif any(svc in service_lower for svc in ['productcatalog', 'recommendation', 'ad']):
                return duration_ms > 500   # 500毫秒

            # 4. 业务处理服务：中等阈值
            elif any(svc in service_lower for svc in ['checkout', 'payment', 'shipping', 'email']):
                return duration_ms > 800   # 800毫秒

            # 5. 汇率服务：快速响应
            elif 'currency' in service_lower:
                return duration_ms > 200   # 200毫秒

            # 6. 通用阈值
            else:
                return duration_ms > 1000  # 1秒

        except Exception as e:
            self.logger.debug(f"智能异常检测失败: {e}")
            # 降级到简单阈值
            return duration_ms > 1000

    def _intelligent_duration_fault_detection(self, traces_data: pd.DataFrame) -> List[Dict[str, Any]]:
        """基于HipsterShop架构的智能duration故障检测 - 🔧 基于实际trace字段"""
        duration_anomalies = []

        try:
            if 'duration' not in traces_data.columns:
                self.logger.warning("⚠️ traces数据中缺少duration字段")
                return duration_anomalies

            self.logger.info(f"🔍 开始智能duration故障检测: {len(traces_data)}条trace")

            # 遍历每个trace进行检测
            for idx, trace in traces_data.iterrows():
                try:
                    # 提取关键字段
                    duration_us = trace.get('duration', 0)  # 微秒
                    duration_ms = duration_us / 1000  # 转换为毫秒

                    # 从process字段提取服务名
                    service_name = self._extract_service_name_from_process_field(trace)

                    # 从process字段提取Pod名
                    pod_instance = self._extract_pod_name_from_process(trace)

                    # 提取操作名称
                    operation_name = trace.get('operationName', 'unknown')

                    # 提取traceID和spanID
                    trace_id = trace.get('traceID', 'unknown')
                    span_id = trace.get('spanID', 'unknown')

                    # 使用智能阈值判断是否异常
                    if self._is_span_anomalous_by_service_type(service_name, duration_ms, trace):
                        # 确定严重程度
                        if duration_ms > 10000:  # 10秒
                            severity = 'critical'
                        elif duration_ms > 5000:  # 5秒
                            severity = 'high'
                        elif duration_ms > 2000:  # 2秒
                            severity = 'medium'
                        else:
                            severity = 'low'

                        duration_anomalies.append({
                            "type": "intelligent_duration_fault",
                            "service": service_name,
                            "pod_instance": pod_instance,
                            "trace_id": trace_id,
                            "span_id": span_id,
                            "operation_name": operation_name,
                            "duration_ms": duration_ms,
                            "severity": severity,
                            "anomaly_type": "service_latency",
                            "description": f"{service_name}服务响应延迟异常: {duration_ms:.1f}ms",
                            "evidence": f"基于{service_name}服务特性的智能阈值检测",
                            "source": "intelligent_duration_detection"
                        })

                except Exception as e:
                    self.logger.debug(f"处理trace {idx}时出错: {e}")
                    continue

            self.logger.info(f"✅ 智能duration故障检测完成: {len(duration_anomalies)}/{len(traces_data)} 个异常")
            return duration_anomalies

        except Exception as e:
            self.logger.error(f"❌ 智能duration故障检测失败: {e}")
            return []

    def _extract_service_name_from_process_field(self, trace) -> str:
        """从process字段中提取serviceName - 🔧 基于实际数据结构"""
        try:
            process = trace.get('process', {})
            if isinstance(process, dict):
                return process.get('serviceName', 'unknown')
            elif isinstance(process, str):
                # 如果process是字符串，尝试解析JSON
                import json
                try:
                    process_dict = json.loads(process)
                    return process_dict.get('serviceName', 'unknown')
                except:
                    return 'unknown'
            return 'unknown'
        except Exception as e:
            self.logger.debug(f"提取serviceName失败: {e}")
            return 'unknown'

    def _trace_chain_fault_propagation_detection(self, traces_data: pd.DataFrame) -> List[Dict[str, Any]]:
        """基于调用链关联的故障传播检测 - 🔧 基于实际trace字段"""
        trace_chain_faults = []

        try:
            if 'traceID' not in traces_data.columns:
                self.logger.warning("⚠️ traces数据中缺少traceID字段")
                return trace_chain_faults

            self.logger.info(f"🔍 开始调用链故障传播检测: {len(traces_data)}条trace")

            # 按traceID分组分析
            trace_groups = traces_data.groupby('traceID')

            for trace_id, group in trace_groups:
                if len(group) < 2:  # 单span的trace跳过
                    continue

                try:
                    # 分析这个调用链中的故障传播
                    chain_fault = self._analyze_single_trace_chain_fault(trace_id, group)
                    if chain_fault:
                        trace_chain_faults.append(chain_fault)

                except Exception as e:
                    self.logger.debug(f"分析调用链 {trace_id} 时出错: {e}")
                    continue

            self.logger.info(f"✅ 调用链故障传播检测完成: {len(trace_chain_faults)} 个故障链")
            return trace_chain_faults

        except Exception as e:
            self.logger.error(f"❌ 调用链故障传播检测失败: {e}")
            return []

    def _analyze_single_trace_chain_fault(self, trace_id: str, group: pd.DataFrame) -> Dict[str, Any]:
        """分析单个调用链中的故障传播 - 🔧 基于实际trace字段"""
        try:
            # 提取调用链中的关键信息
            services_in_chain = []
            total_duration = 0
            error_spans = []
            latency_spans = []

            for _, span in group.iterrows():
                service_name = self._extract_service_name_from_process_field(span)
                duration_ms = span.get('duration', 0) / 1000

                services_in_chain.append(service_name)
                total_duration += duration_ms

                # 检查是否有错误
                if self._check_span_error(span):
                    error_spans.append({
                        'service': service_name,
                        'span_id': span.get('spanID', 'unknown'),
                        'duration_ms': duration_ms
                    })

                # 检查是否有延迟异常
                if self._is_span_anomalous_by_service_type(service_name, duration_ms, span):
                    latency_spans.append({
                        'service': service_name,
                        'span_id': span.get('spanID', 'unknown'),
                        'duration_ms': duration_ms
                    })

            # 判断是否构成故障链
            if len(error_spans) > 0 or len(latency_spans) > 1:  # 有错误或多个延迟异常
                # 确定严重程度
                if len(error_spans) > 0:
                    severity = 'critical'
                elif total_duration > 10000:  # 总延迟超过10秒
                    severity = 'high'
                elif len(latency_spans) > 2:  # 多个服务延迟
                    severity = 'medium'
                else:
                    severity = 'low'

                return {
                    "type": "trace_chain_fault",
                    "trace_id": trace_id,
                    "services_in_chain": list(set(services_in_chain)),
                    "total_duration_ms": total_duration,
                    "error_spans": error_spans,
                    "latency_spans": latency_spans,
                    "span_count": len(group),
                    "severity": severity,
                    "description": f"调用链故障: {len(error_spans)}个错误span, {len(latency_spans)}个延迟span",
                    "evidence": f"TraceID {trace_id} 中检测到故障传播",
                    "source": "trace_chain_fault_detection"
                }

            return None

        except Exception as e:
            self.logger.debug(f"分析调用链故障失败: {e}")
            return None

    def _aggregate_service_fault_summary(self, error_evidence: List, duration_anomalies: List, trace_chain_faults: List) -> Dict[str, Any]:
        """聚合服务级别的故障总结 - 🔧 基于检测结果"""
        service_fault_summary = {}

        try:
            # 聚合错误证据
            for evidence in error_evidence:
                service = evidence.get('service', 'unknown')
                if service not in service_fault_summary:
                    service_fault_summary[service] = {
                        'error_count': 0,
                        'duration_anomaly_count': 0,
                        'trace_chain_fault_count': 0,
                        'max_severity': 'low',
                        'fault_types': set(),
                        'evidence_details': []
                    }

                service_fault_summary[service]['error_count'] += 1
                service_fault_summary[service]['fault_types'].add(evidence.get('error_type', 'unknown'))
                service_fault_summary[service]['evidence_details'].append(evidence.get('description', ''))

                # 更新最高严重程度
                severity = evidence.get('severity', 'low')
                if self._compare_severity(severity, service_fault_summary[service]['max_severity']) > 0:
                    service_fault_summary[service]['max_severity'] = severity

            # 聚合duration异常
            for anomaly in duration_anomalies:
                service = anomaly.get('service', 'unknown')
                if service not in service_fault_summary:
                    service_fault_summary[service] = {
                        'error_count': 0,
                        'duration_anomaly_count': 0,
                        'trace_chain_fault_count': 0,
                        'max_severity': 'low',
                        'fault_types': set(),
                        'evidence_details': []
                    }

                service_fault_summary[service]['duration_anomaly_count'] += 1
                service_fault_summary[service]['fault_types'].add('duration_anomaly')
                service_fault_summary[service]['evidence_details'].append(anomaly.get('description', ''))

                # 更新最高严重程度
                severity = anomaly.get('severity', 'low')
                if self._compare_severity(severity, service_fault_summary[service]['max_severity']) > 0:
                    service_fault_summary[service]['max_severity'] = severity

            # 聚合调用链故障
            for chain_fault in trace_chain_faults:
                services = chain_fault.get('services_in_chain', [])
                for service in services:
                    if service not in service_fault_summary:
                        service_fault_summary[service] = {
                            'error_count': 0,
                            'duration_anomaly_count': 0,
                            'trace_chain_fault_count': 0,
                            'max_severity': 'low',
                            'fault_types': set(),
                            'evidence_details': []
                        }

                    service_fault_summary[service]['trace_chain_fault_count'] += 1
                    service_fault_summary[service]['fault_types'].add('trace_chain_fault')
                    service_fault_summary[service]['evidence_details'].append(chain_fault.get('description', ''))

                    # 更新最高严重程度
                    severity = chain_fault.get('severity', 'low')
                    if self._compare_severity(severity, service_fault_summary[service]['max_severity']) > 0:
                        service_fault_summary[service]['max_severity'] = severity

            # 转换set为list以便JSON序列化
            for service in service_fault_summary:
                service_fault_summary[service]['fault_types'] = list(service_fault_summary[service]['fault_types'])

            return service_fault_summary

        except Exception as e:
            self.logger.error(f"❌ 服务故障聚合失败: {e}")
            return {}

    def _compare_severity(self, severity1: str, severity2: str) -> int:
        """比较严重程度，返回1表示severity1更严重，-1表示severity2更严重，0表示相等"""
        severity_order = {'low': 1, 'medium': 2, 'high': 3, 'critical': 4}
        level1 = severity_order.get(severity1, 1)
        level2 = severity_order.get(severity2, 1)

        if level1 > level2:
            return 1
        elif level1 < level2:
            return -1
        else:
            return 0

    def _extract_tags_summary(self, trace) -> Dict[str, Any]:
        """提取tags字段的关键信息"""
        try:
            tags_summary = {
                'error_indicators': [],
                'status_codes': [],
                'key_values': {}
            }

            tags = trace.get('tags', [])
            if isinstance(tags, str):
                import json
                try:
                    tags = json.loads(tags)
                except:
                    return tags_summary

            if isinstance(tags, list):
                for tag in tags:
                    if isinstance(tag, dict):
                        key = tag.get('key', '')
                        value = tag.get('value', '')

                        # 检查错误指示器
                        if any(error_key in key.lower() for error_key in ['error', 'exception', 'fail']):
                            tags_summary['error_indicators'].append(f"{key}={value}")

                        # 检查状态码
                        if 'status' in key.lower() or 'code' in key.lower():
                            tags_summary['status_codes'].append(f"{key}={value}")

                        # 记录关键值
                        if key in ['http.status_code', 'grpc.status_code', 'error', 'component']:
                            tags_summary['key_values'][key] = value

            return tags_summary

        except Exception as e:
            self.logger.debug(f"提取tags摘要失败: {e}")
            return {'error_indicators': [], 'status_codes': [], 'key_values': {}}

    def _extract_logs_summary(self, trace) -> Dict[str, Any]:
        """提取logs字段的关键信息"""
        try:
            logs_summary = {
                'error_messages': [],
                'event_count': 0,
                'has_errors': False
            }

            logs = trace.get('logs', [])
            if isinstance(logs, str):
                import json
                try:
                    logs = json.loads(logs)
                except:
                    return logs_summary

            if isinstance(logs, list):
                logs_summary['event_count'] = len(logs)

                for log_entry in logs:
                    if isinstance(log_entry, dict):
                        fields = log_entry.get('fields', [])
                        if isinstance(fields, list):
                            for field in fields:
                                if isinstance(field, dict):
                                    key = field.get('key', '')
                                    value = field.get('value', '')

                                    # 检查错误消息
                                    if any(error_key in key.lower() for error_key in ['error', 'exception', 'message']):
                                        if any(error_word in str(value).lower() for error_word in ['error', 'exception', 'fail', 'timeout']):
                                            logs_summary['error_messages'].append(f"{key}: {value}")
                                            logs_summary['has_errors'] = True

            return logs_summary

        except Exception as e:
            self.logger.debug(f"提取logs摘要失败: {e}")
            return {'error_messages': [], 'event_count': 0, 'has_errors': False}


    async def _retrieve_complete_trace_chains(self, anomalous_traces: List[Dict], traces_data: pd.DataFrame) -> Dict[str, List[Dict]]:
        """检索异常traceID的完整调用链"""
        try:
            self.logger.info("🔍 开始检索完整调用链")

            complete_chains = {}

            for anomaly in anomalous_traces:
                trace_id = anomaly.get('traceID')
                if not trace_id or trace_id == 'unknown':
                    continue

                # 从原始数据中检索该traceID的所有span
                trace_spans = traces_data[traces_data['traceID'] == trace_id]

                if not trace_spans.empty:
                    # 构建完整的调用链信息
                    chain_info = []

                    for _, span in trace_spans.iterrows():
                        service_name, pod_instance = self._extract_service_and_pod_from_trace(span)

                        span_info = {
                            'spanID': span.get('spanID', 'unknown'),
                            'parentSpanID': span.get('parentSpanID', ''),
                            'operationName': span.get('operationName', 'unknown'),
                            'service': service_name,
                            'pod_instance': pod_instance,
                            'duration_ms': span.get('duration', 0) / 1000 if pd.notna(span.get('duration')) else 0,
                            'startTime': span.get('startTimeMillis', 0),
                            'tags': self._extract_tags_summary(span),
                            'logs': self._extract_logs_summary(span),
                            'raw_data': {
                                'traceID': span.get('traceID'),
                                'spanID': span.get('spanID'),
                                'operationName': span.get('operationName'),
                                'duration': span.get('duration'),
                                'startTimeMillis': span.get('startTimeMillis'),
                                'process': span.get('process', ''),
                                'tags': span.get('tags', []),
                                'logs': span.get('logs', [])
                            }
                        }
                        chain_info.append(span_info)

                    # 按时间排序
                    chain_info.sort(key=lambda x: x.get('startTime', 0))
                    complete_chains[trace_id] = chain_info

                    self.logger.info(f"✅ 检索到traceID {trace_id} 的完整调用链: {len(chain_info)} 个span")

            return complete_chains

        except Exception as e:
            self.logger.error(f"❌ 检索完整调用链失败: {e}")
            return {}


    def _extract_service_and_pod_from_trace(self, trace_row) -> tuple:
        """从trace记录中提取服务名称和Pod实例信息"""
        try:
            service_name = "unknown"
            pod_instance = "unknown"

            # 尝试从process字段中提取详细信息
            if 'process' in trace_row and pd.notna(trace_row['process']):
                import json
                try:
                    process_data = json.loads(str(trace_row['process']))

                    # 提取serviceName
                    if 'serviceName' in process_data:
                        service_name = process_data['serviceName']

                    # 提取Pod实例信息
                    if 'tags' in process_data:
                        for tag in process_data['tags']:
                            if tag.get('key') == 'hostname':
                                pod_instance = tag.get('value', 'unknown')
                                break
                            elif tag.get('key') == 'pod.name':
                                pod_instance = tag.get('value', 'unknown')
                                break
                            elif tag.get('key') == 'k8s.pod.name':
                                pod_instance = tag.get('value', 'unknown')
                                break

                except Exception as e:
                    self.logger.debug(f"Failed to parse process JSON: {e}")

            # 尝试从operationName中提取服务名称（如果process中没有）
            if service_name == "unknown" and 'operationName' in trace_row and pd.notna(trace_row['operationName']):
                operation = str(trace_row['operationName'])
                # 格式如: hipstershop.ProductCatalogService/GetProduct
                if 'hipstershop.' in operation:
                    service_part = operation.split('hipstershop.')[1].split('/')[0]
                    # 转换为小写并移除Service后缀
                    if service_part.endswith('Service'):
                        service_part = service_part[:-7]  # 移除'Service'
                    service_name = service_part.lower()

                    # 映射到已知的HipsterShop服务
                    service_mapping = {
                        'productcatalog': 'productcatalogservice',
                        'currency': 'currencyservice',
                        'frontend': 'frontend',
                        'checkout': 'checkoutservice',
                        'payment': 'paymentservice',
                        'cart': 'cartservice',
                        'recommendation': 'recommendationservice',
                        'ad': 'adservice',
                        'shipping': 'shippingservice',
                        'email': 'emailservice'
                    }

                    service_name = service_mapping.get(service_name, service_name)

            return service_name, pod_instance

        except Exception as e:
            self.logger.warning(f"Failed to extract service and pod from trace: {e}")
            return "unknown", "unknown"



    def _build_trace_analysis_data(self, traces_data: pd.DataFrame, analysis_results: Dict[str, Any]) -> Dict:
        """构建调用链分析数据"""
        analysis_data = {}

        try:
            if not traces_data.empty:
                analysis_data['time_range'] = f"{traces_data['startTimeMillis'].min()} 到 {traces_data['startTimeMillis'].max()}"
                analysis_data['total_spans'] = len(traces_data)

                # 分析duration分布
                if 'duration' in traces_data.columns:
                    durations = traces_data['duration'].dropna()
                    analysis_data['duration_stats'] = {
                        'mean': float(durations.mean()),
                        'max': float(durations.max()),
                        'min': float(durations.min()),
                        'p95': float(durations.quantile(0.95)),
                        'p99': float(durations.quantile(0.99))
                    }

                # 分析操作分布
                if 'operationName' in traces_data.columns:
                    operations = traces_data['operationName'].value_counts()
                    analysis_data['top_operations'] = operations.head(10).to_dict()

        except Exception as e:
            self.logger.error(f"构建调用链分析数据失败: {e}")

        return analysis_data



    def _extract_raw_trace_data_for_service(self, raw_traces_data: pd.DataFrame,
                                          service_name: str, start_time: str, end_time: str) -> List[Dict[str, Any]]:
        """为特定服务提取原始Trace数据记录"""
        if raw_traces_data is None or raw_traces_data.empty:
            return []

        try:
            # 过滤相关数据
            filtered_data = raw_traces_data.copy()

            # 按服务名过滤
            if 'operationName' in filtered_data.columns:
                filtered_data = filtered_data[
                    filtered_data['operationName'].str.contains(service_name, na=False, case=False)
                ]
            elif 'serviceName' in filtered_data.columns:
                filtered_data = filtered_data[
                    filtered_data['serviceName'].str.contains(service_name, na=False, case=False)
                ]

            # 时间过滤
            if 'startTime' in filtered_data.columns:
                # 转换时间戳进行过滤
                start_ts = pd.to_datetime(start_time).timestamp() * 1000000  # 微秒
                end_ts = pd.to_datetime(end_time).timestamp() * 1000000
                filtered_data = filtered_data[
                    (filtered_data['startTime'] >= start_ts) &
                    (filtered_data['startTime'] <= end_ts)
                ]

            # 转换为字典列表，提供更多完整数据
            records = filtered_data.head(100).to_dict('records')  # 增加到100条记录
            self.logger.info(f"为服务 {service_name} 提取了 {len(records)} 条原始trace记录")
            return records

        except Exception as e:
            self.logger.warning(f"提取原始Trace数据失败: {e}")
            return []


    def _build_complete_trace_context(self, raw_traces_data: pd.DataFrame,
                                    anomalies: List[Dict[str, Any]]) -> Dict[str, Any]:
        """构建完整的调用链上下文数据"""
        if raw_traces_data is None or raw_traces_data.empty:
            return {"complete_traces": [], "service_dependencies": {}, "error_propagation_paths": []}

        try:
            complete_trace_context = {
                "complete_traces": [],
                "service_dependencies": {},
                "error_propagation_paths": [],
                "trace_statistics": {}
            }

            # 为每个异常提取完整的调用链上下文
            for anomaly in anomalies[:5]:  # 处理前5个异常
                service_name = anomaly.get("service", "unknown")
                trace_id = anomaly.get("trace_id")

                if trace_id:
                    # 根据traceID提取完整调用链
                    complete_trace = self._extract_complete_trace_by_id(raw_traces_data, trace_id)
                    if complete_trace:
                        complete_trace_context["complete_traces"].append({
                            "trace_id": trace_id,
                            "anomalous_service": service_name,
                            "spans": complete_trace,
                            "service_call_path": self._build_service_call_path(complete_trace),
                            "timing_analysis": self._analyze_trace_timing(complete_trace),
                            "error_spans": self._identify_error_spans(complete_trace)
                        })

            return complete_trace_context

        except Exception as e:
            self.logger.error(f"构建完整调用链上下文失败: {e}")
            return {"complete_traces": [], "service_dependencies": {}, "error_propagation_paths": []}

    def _extract_complete_trace_by_id(self, traces_data: pd.DataFrame, trace_id: str) -> List[Dict[str, Any]]:
        """根据traceID提取完整的调用链数据"""
        try:
            trace_spans = traces_data[traces_data['traceID'] == trace_id]
            if trace_spans.empty:
                return []

            # 按时间排序spans
            if 'startTime' in trace_spans.columns:
                trace_spans = trace_spans.sort_values('startTime')

            # 转换为字典列表，包含完整的span信息
            complete_spans = []
            for _, span in trace_spans.iterrows():
                span_info = {
                    "span_id": span.get('spanID', 'unknown'),
                    "parent_span_id": span.get('parentSpanID', 'unknown'),
                    "operation_name": span.get('operationName', 'unknown'),
                    "service_name": self._extract_service_name_from_span(span),
                    "start_time": span.get('startTime', 0),
                    "duration": span.get('duration', 0),
                    "tags": self._extract_span_tags(span),
                    "logs": self._extract_span_logs(span),
                    "has_error": self._check_span_error(span)
                }
                complete_spans.append(span_info)

            return complete_spans

        except Exception as e:
            self.logger.error(f"提取完整调用链失败: {e}")
            return []

    def _build_service_call_path(self, complete_spans: List[Dict[str, Any]]) -> List[str]:
        """构建服务调用路径"""
        try:
            # 按时间排序并提取服务调用顺序
            sorted_spans = sorted(complete_spans, key=lambda x: x.get('start_time', 0))

            call_path = []
            seen_services = set()

            for span in sorted_spans:
                service = span.get('service_name', 'unknown')
                operation = span.get('operation_name', 'unknown')

                if service not in seen_services:
                    call_path.append(f"{service}::{operation}")
                    seen_services.add(service)

            return call_path

        except Exception as e:
            self.logger.error(f"构建服务调用路径失败: {e}")
            return []

    def _analyze_trace_timing(self, complete_spans: List[Dict[str, Any]]) -> Dict[str, Any]:
        """分析调用链时序信息"""
        try:
            if not complete_spans:
                return {}

            durations = [span.get('duration', 0) for span in complete_spans]
            total_duration = sum(durations)

            # 找到最耗时的span
            slowest_span = max(complete_spans, key=lambda x: x.get('duration', 0))

            return {
                "total_duration_ms": total_duration / 1000,  # 转换为毫秒
                "span_count": len(complete_spans),
                "slowest_span": {
                    "service": slowest_span.get('service_name', 'unknown'),
                    "operation": slowest_span.get('operation_name', 'unknown'),
                    "duration_ms": slowest_span.get('duration', 0) / 1000
                },
                "duration_distribution": {
                    "min_ms": min(durations) / 1000 if durations else 0,
                    "max_ms": max(durations) / 1000 if durations else 0,
                    "avg_ms": (sum(durations) / len(durations)) / 1000 if durations else 0
                }
            }

        except Exception as e:
            self.logger.error(f"分析调用链时序失败: {e}")
            return {}

    def _identify_error_spans(self, complete_spans: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """识别包含错误的spans"""
        try:
            error_spans = []

            for span in complete_spans:
                if span.get('has_error', False):
                    error_spans.append({
                        "span_id": span.get('span_id', 'unknown'),
                        "service": span.get('service_name', 'unknown'),
                        "operation": span.get('operation_name', 'unknown'),
                        "error_tags": [tag for tag in span.get('tags', []) if 'error' in str(tag).lower()],
                        "error_logs": [log for log in span.get('logs', []) if 'error' in str(log).lower()]
                    })

            return error_spans

        except Exception as e:
            self.logger.error(f"识别错误spans失败: {e}")
            return []


    def _extract_service_name_from_span(self, span: pd.Series) -> str:
        """从span中提取服务名 - 优先提取Pod级别信息"""
        try:
            if 'process' in span and pd.notna(span['process']):
                import json
                process_data = json.loads(str(span['process']))

                # 优先提取Pod名称（更精确的故障定位）
                pod_name = self._extract_pod_name_from_process(process_data)
                if pod_name and pod_name != 'unknown':
                    return pod_name

                # 回退到Service名称
                return process_data.get('serviceName', 'unknown')
            return 'unknown'
        except Exception:
            return 'unknown'

    def _extract_pod_name_from_process(self, process_data: dict) -> Optional[str]:
        """从process数据中提取Pod名称"""
        try:
            if 'tags' in process_data:
                for tag in process_data['tags']:
                    if isinstance(tag, dict):
                        # 查找Pod名称标签
                        if tag.get('key') == 'name' and tag.get('value'):
                            pod_name = tag['value']
                            # 验证是否为有效的Pod名称
                            if self._is_valid_pod_name(pod_name):
                                return pod_name

                        # 查找hostname标签（可能包含Pod信息）
                        elif tag.get('key') == 'hostname' and tag.get('value'):
                            hostname = tag['value']
                            if self._is_valid_pod_name(hostname):
                                return hostname

            return None
        except Exception:
            return None


    def _extract_span_tags(self, span: pd.Series) -> List[Dict[str, Any]]:
        """提取span的tags信息"""
        try:
            if 'tags' in span and pd.notna(span['tags']):
                import json
                tags_data = json.loads(str(span['tags']))
                if isinstance(tags_data, list):
                    return tags_data
            return []
        except Exception:
            return []

    def _extract_span_logs(self, span: pd.Series) -> List[Dict[str, Any]]:
        """提取span的logs信息"""
        try:
            if 'logs' in span and pd.notna(span['logs']):
                import json
                logs_data = json.loads(str(span['logs']))
                if isinstance(logs_data, list):
                    return logs_data
            return []
        except Exception:
            return []

    def _check_span_error(self, span: pd.Series) -> bool:
        """检查span是否包含错误"""
        try:
            # 检查tags中的error标记
            tags = self._extract_span_tags(span)
            for tag in tags:
                if isinstance(tag, dict):
                    key = tag.get('key', '').lower()
                    value = str(tag.get('value', '')).lower()
                    if key == 'error' and value in ['true', '1']:
                        return True
                    if 'error' in key or 'error' in value:
                        return True

            # 检查logs中的错误信息
            logs = self._extract_span_logs(span)
            for log in logs:
                if isinstance(log, dict):
                    fields = log.get('fields', [])
                    for field in fields:
                        if isinstance(field, dict):
                            key = field.get('key', '').lower()
                            value = str(field.get('value', '')).lower()
                            if 'error' in key or 'error' in value:
                                return True

            return False
        except Exception:
            return False


    def _manual_component_mapping(self, raw_component: str, data_source: str) -> Optional[str]:
        """手动组件映射（处理特殊情况）"""

        # 从operationName中提取服务名
        if 'hipstershop.' in raw_component:
            # 处理类似 "hipstershop.CartService/GetCart" 的情况
            parts = raw_component.split('.')
            if len(parts) >= 2:
                service_part = parts[1].split('/')[0]  # 获取 "CartService"
                # 转换为标准服务名
                service_mapping = {
                    'CartService': 'cartservice',
                    'CheckoutService': 'checkoutservice',
                    'PaymentService': 'paymentservice',
                    'ProductCatalogService': 'productcatalogservice',
                    'RecommendationService': 'recommendationservice',
                    'AdService': 'adservice',
                    'CurrencyService': 'currencyservice',
                    'ShippingService': 'shippingservice',
                    'EmailService': 'emailservice'
                }

                if service_part in service_mapping:
                    return service_mapping[service_part]

        # 处理直接的服务名
        if raw_component.lower().endswith('service'):
            return raw_component.lower()

        return None
