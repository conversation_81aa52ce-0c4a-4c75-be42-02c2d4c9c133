"""
自适应阈值管理器 - 基于历史数据动态计算阈值
"""

import pandas as pd
import numpy as np
import os
import json
import yaml
from datetime import datetime, timedelta
from typing import Dict, Any, Optional, List
import logging
from pathlib import Path


class AdaptiveThresholdManager:
    """自适应阈值管理器"""
    
    def __init__(self, data_root: str = "/data/data",
                 cache_dir: str = "threshold_cache",
                 config_path: str = None,
                 lookback_days: int = 7):
        self.data_root = data_root
        self.cache_dir = Path(cache_dir)
        self.cache_dir.mkdir(exist_ok=True)
        self.lookback_days = lookback_days
        self.logger = logging.getLogger(__name__)

        # 加载配置文件
        self.config = self._load_config(config_path)
        self.lookback_days = self.config.get('global', {}).get('lookback_days', lookback_days)

    def _load_config(self, config_path: str = None) -> Dict[str, Any]:
        """加载配置文件"""
        if config_path is None:
            # 使用默认配置路径
            current_dir = Path(__file__).parent.parent
            config_path = current_dir / "config" / "adaptive_threshold_config.yaml"

        try:
            if os.path.exists(config_path):
                with open(config_path, 'r', encoding='utf-8') as f:
                    config = yaml.safe_load(f)
                self.logger.info(f"✅ 加载自适应阈值配置: {config_path}")
                return config
            else:
                self.logger.warning(f"⚠️ 配置文件不存在，使用默认配置: {config_path}")
                return self._get_default_config()
        except Exception as e:
            self.logger.error(f"❌ 加载配置文件失败: {e}")
            return self._get_default_config()

    def _get_default_config(self) -> Dict[str, Any]:
        """获取默认配置"""
        return {
            'global': {'lookback_days': 7, 'cache_duration_hours': 1},
            'metric_type_defaults': {
                'cpu': {'strategy': 'percentile', 'percentile': 0.95, 'min_threshold': 0.8},
                'memory': {'strategy': 'percentile', 'percentile': 0.90, 'safety_margin': 1.2},
                'io': {'strategy': 'percentile', 'percentile': 0.95, 'min_threshold': 0.8},
                'qps': {'strategy': 'statistical', 'sigma': 3, 'min_threshold': 100},
                'error': {'strategy': 'percentile', 'percentile': 0.99, 'min_threshold': 0.05},
                'latency': {'strategy': 'percentile', 'percentile': 0.95, 'min_threshold': 100000},
            },
            'fallback': {
                'default_thresholds': {
                    'cpu_usage': 80.0,
                    'memory_usage': 2147483648,  # 2GB
                    'io_util': 80.0,
                    'qps': 1000,
                    'latency': 1000000,  # 1s in μs
                    'error_ratio': 0.05
                }
            }
        }
    
    def get_adaptive_threshold(self, component_type: str, metric_name: str, 
                             current_time: str) -> float:
        """获取自适应阈值"""
        try:
            # 1. 检查缓存
            cached_threshold = self._get_cached_threshold(component_type, metric_name, current_time)
            if cached_threshold is not None:
                return cached_threshold
            
            # 2. 计算新阈值
            threshold = self._calculate_adaptive_threshold(component_type, metric_name, current_time)
            
            # 3. 缓存结果
            self._cache_threshold(component_type, metric_name, current_time, threshold)
            
            return threshold
            
        except Exception as e:
            self.logger.error(f"获取自适应阈值失败 {component_type}.{metric_name}: {e}")
            return self._get_fallback_threshold(metric_name)
    
    def _calculate_adaptive_threshold(self, component_type: str, metric_name: str, 
                                    current_time: str) -> float:
        """计算自适应阈值"""
        # 1. 加载历史数据
        historical_data = self._load_historical_data(component_type, metric_name, current_time)
        
        if historical_data.empty:
            # 🔧 优化：使用debug级别减少日志噪音
            self.logger.debug(f"无历史数据，使用fallback阈值: {component_type}.{metric_name}")
            return self._get_fallback_threshold(metric_name)
        
        # 2. 获取阈值策略
        strategy = self._get_threshold_strategy(component_type, metric_name)
        
        # 3. 根据策略计算阈值
        if strategy.get('strategy') == 'percentile':
            percentile = strategy.get('percentile', 0.95)
            threshold = historical_data.quantile(percentile)

            # 应用安全边界
            if 'safety_margin' in strategy:
                threshold *= strategy['safety_margin']

        elif strategy.get('strategy') == 'statistical':
            mean_val = historical_data.mean()
            std_val = historical_data.std()
            sigma = strategy.get('sigma', 3)
            threshold = mean_val + sigma * std_val

        else:
            # 默认使用95分位数
            threshold = historical_data.quantile(0.95)
        
        # 4. 应用最小阈值保护
        if 'min_threshold' in strategy:
            threshold = max(threshold, strategy['min_threshold'])
        
        # 5. 特殊处理
        threshold = self._apply_special_rules(component_type, metric_name, threshold, historical_data)
        
        self.logger.info(f"🧠 计算自适应阈值: {component_type}.{metric_name} = {threshold:.6f}")
        self.logger.info(f"   历史数据: {len(historical_data)}个点, 范围[{historical_data.min():.6f}, {historical_data.max():.6f}]")
        self.logger.info(f"   策略: {strategy}")
        
        return threshold
    
    def _load_historical_data(self, component_type: str, metric_name: str, 
                            current_time: str) -> pd.Series:
        """加载历史数据"""
        try:
            current_dt = pd.to_datetime(current_time)
            historical_data = []
            
            # 查找过去N天的数据
            for i in range(1, self.lookback_days + 1):
                target_date = current_dt - timedelta(days=i)
                date_str = target_date.strftime('%Y-%m-%d')
                
                # 构建文件路径
                if component_type in ['tikv', 'pd', 'tidb']:
                    file_pattern = f"infra_{component_type}_{metric_name}_{date_str}.parquet"
                    file_path = os.path.join(self.data_root, date_str, "metric-parquet", "other", file_pattern)
                else:
                    # APM或基础设施数据
                    continue  # 暂时跳过，专注于TiKV等组件
                
                if os.path.exists(file_path):
                    df = pd.read_parquet(file_path)
                    
                    # 提取数值列
                    value_cols = [col for col in df.columns if col in [metric_name, 'value', 'metric_value']]
                    if value_cols:
                        values = df[value_cols[0]].dropna()
                        historical_data.extend(values.tolist())
            
            return pd.Series(historical_data)
            
        except Exception as e:
            self.logger.error(f"加载历史数据失败 {component_type}.{metric_name}: {e}")
            return pd.Series([])

    def _get_threshold_strategy(self, component_type: str, metric_name: str) -> Dict[str, Any]:
        """获取阈值策略"""
        # 1. 优先使用组件特定配置
        components_config = self.config.get('components', {})
        if component_type in components_config:
            component_config = components_config[component_type]
            if metric_name in component_config:
                return component_config[metric_name]

        # 2. 使用指标类型默认配置
        metric_type = self._identify_metric_type(metric_name)
        metric_defaults = self.config.get('metric_type_defaults', {})
        if metric_type in metric_defaults:
            return metric_defaults[metric_type]

        # 3. 使用CPU类型作为最终默认
        return metric_defaults.get('cpu', {
            'strategy': 'percentile', 'percentile': 0.95, 'min_threshold': 0.8
        })

    def _identify_metric_type(self, metric_name: str) -> str:
        """识别指标类型"""
        metric_lower = metric_name.lower()
        
        if any(keyword in metric_lower for keyword in ['cpu', 'processor']):
            return 'cpu'
        elif any(keyword in metric_lower for keyword in ['memory', 'mem', 'ram']):
            return 'memory'
        elif any(keyword in metric_lower for keyword in ['io', 'disk', 'storage']):
            return 'io'
        elif any(keyword in metric_lower for keyword in ['qps', 'rps', 'throughput', 'request']):
            return 'qps'
        elif any(keyword in metric_lower for keyword in ['error', 'fail', 'exception']):
            return 'error'
        elif any(keyword in metric_lower for keyword in ['latency', 'duration', 'time', 'delay']):
            return 'latency'
        else:
            return 'cpu'  # 默认策略
    
    def _apply_special_rules(self, component_type: str, metric_name: str, 
                           threshold: float, historical_data: pd.Series) -> float:
        """应用特殊规则"""
        # TiKV特殊规则
        if component_type == 'tikv':
            if metric_name == 'memory_usage':
                # TiKV内存：不应超过可用内存的80%
                max_memory = 8 * 1024 * 1024 * 1024  # 假设8GB可用内存
                threshold = min(threshold, max_memory * 0.8)
            elif metric_name == 'cpu_usage':
                # TiKV CPU：转换为百分比形式
                if threshold < 1.0:  # 如果是小数形式
                    threshold = min(threshold * 100, 80.0)  # 最高80%
            elif metric_name == 'io_util':
                # IO利用率：转换为百分比形式
                if threshold < 1.0:
                    threshold = min(threshold * 100, 80.0)
        
        return threshold
    
    def _get_cached_threshold(self, component_type: str, metric_name: str, 
                            current_time: str) -> Optional[float]:
        """获取缓存的阈值"""
        try:
            cache_file = self.cache_dir / f"{component_type}_{metric_name}.json"
            if not cache_file.exists():
                return None
            
            with open(cache_file, 'r') as f:
                cache_data = json.load(f)
            
            # 检查缓存是否过期（1小时）
            cache_time = pd.to_datetime(cache_data['timestamp'])
            current_dt = pd.to_datetime(current_time)
            
            if (current_dt - cache_time).total_seconds() < 3600:  # 1小时内有效
                return cache_data['threshold']
            
            return None
            
        except Exception as e:
            self.logger.debug(f"读取缓存失败: {e}")
            return None
    
    def _cache_threshold(self, component_type: str, metric_name: str, 
                        current_time: str, threshold: float):
        """缓存阈值"""
        try:
            cache_file = self.cache_dir / f"{component_type}_{metric_name}.json"
            cache_data = {
                'component_type': component_type,
                'metric_name': metric_name,
                'threshold': threshold,
                'timestamp': current_time
            }
            
            with open(cache_file, 'w') as f:
                json.dump(cache_data, f, indent=2)
                
        except Exception as e:
            self.logger.debug(f"缓存阈值失败: {e}")
    
    def _get_fallback_threshold(self, metric_name: str) -> float:
        """获取fallback阈值"""
        # 1. 优先使用配置文件中的fallback阈值
        fallback_config = self.config.get('fallback', {}).get('default_thresholds', {})
        if metric_name in fallback_config:
            return fallback_config[metric_name]

        # 2. 根据指标类型获取默认值
        metric_type = self._identify_metric_type(metric_name)
        type_fallbacks = {
            'cpu': fallback_config.get('cpu_usage', 80.0),
            'memory': fallback_config.get('memory_usage', 2147483648),  # 2GB
            'io': fallback_config.get('io_util', 80.0),
            'qps': fallback_config.get('qps', 10000),  # 🔧 修复：QPS阈值提高到1万
            'error': fallback_config.get('error_ratio', 0.05),
            'latency': fallback_config.get('latency', 1000000),  # 1s in μs
        }

        # 3. 🔧 新增：根据指标名称获取特定默认值
        if 'request' in metric_name or 'response' in metric_name:
            return fallback_config.get('request', 10000)
        elif 'fs_writes_bytes' in metric_name or 'fs_reads_bytes' in metric_name:
            return fallback_config.get('fs_writes_bytes', 102400)  # 100KB (更敏感)
        elif 'network' in metric_name and 'packets' in metric_name:
            return fallback_config.get('network_packets', 100000)
        elif 'network' in metric_name and 'bytes' in metric_name:
            return fallback_config.get('network_bytes', 104857600)  # 100MB
        elif 'processes' in metric_name:
            return fallback_config.get('processes', 1000)
        elif 'tcp' in metric_name and 'inuse' in metric_name:
            return fallback_config.get('tcp_inuse', 10000)

        # 4. 🔧 新增：TiKV特定指标的fallback阈值
        elif 'store_size' in metric_name or 'storage_size' in metric_name:
            return fallback_config.get('store_size', 100*1024*1024*1024)  # 100GB
        elif 'region_pending' in metric_name:
            return fallback_config.get('region_pending', 1000000)  # 100万个region
        elif 'read_mbps' in metric_name:
            return fallback_config.get('read_mbps', 10*1024*1024*1024)  # 10GB/s
        elif 'write_wal_mbps' in metric_name:
            return fallback_config.get('write_wal_mbps', 1*1024*1024*1024)  # 1GB/s
        elif 'region_count' in metric_name:
            return fallback_config.get('region_count', 100000)  # 10万个region
        elif 'store_up_count' in metric_name:
            return fallback_config.get('store_up_count', 100)  # 100个存储节点
        elif 'store_unhealth_count' in metric_name:
            return fallback_config.get('store_unhealth_count', 10)  # 10个不健康节点

        return type_fallbacks.get(metric_type, 5000.0)  # 🔧 修复：通用默认值提高到5000
