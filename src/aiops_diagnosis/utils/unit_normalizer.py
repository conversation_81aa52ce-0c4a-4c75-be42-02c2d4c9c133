#!/usr/bin/env python3
"""
单位规范化工具类
确保整个AIOps系统中的数据单位一致性，符合DeepFlow官方文档规范

核心原则：
1. 时延相关指标统一使用微秒（μs）作为单位
2. 字节相关指标统一使用字节（Bytes）作为单位  
3. 比例相关指标统一使用百分比（%）作为单位
4. 计数相关指标统一使用个数作为单位

作者: AI Assistant
日期: 2025-07-24
"""

from typing import Union, Dict, Any
import logging

logger = logging.getLogger(__name__)


class UnitNormalizer:
    """单位规范化工具类"""
    
    # 时间单位转换常量（目标单位：微秒）
    TIME_UNITS = {
        'ns': 0.001,      # 纳秒 -> 微秒
        'μs': 1.0,        # 微秒 -> 微秒
        'us': 1.0,        # 微秒 -> 微秒  
        'ms': 1000.0,     # 毫秒 -> 微秒
        's': 1000000.0,   # 秒 -> 微秒
    }
    
    # 字节单位转换常量（目标单位：字节）
    BYTE_UNITS = {
        'B': 1,
        'KB': 1024,
        'MB': 1024 * 1024,
        'GB': 1024 * 1024 * 1024,
        'bytes': 1,
    }
    
    # 标准化的指标单位映射
    METRIC_UNITS = {
        # 时延相关指标 - 统一使用微秒
        'rrt': 'μs',
        'rrt_max': 'μs', 
        'duration': 'μs',
        'duration_avg': 'μs',
        'duration_95th': 'μs',
        'duration_99th': 'μs',
        'latency': 'μs',
        
        # 比例相关指标 - 统一使用百分比
        'error_ratio': '%',
        'client_error_ratio': '%',
        'server_error_ratio': '%',
        'timeout_ratio': '%',
        'cpu_usage': '%',
        # 🔧 移除memory_usage的精确匹配，让模糊匹配处理（TiKV的memory_usage是字节，Node的memory_usage_rate是百分比）
        'usage_rate': '%',
        'node_memory_usage_rate': '%',  # 明确Node内存使用率为百分比
        
        # 字节相关指标 - 统一使用字节
        'memory_working_set_bytes': 'bytes',
        'fs_reads_bytes': 'bytes',
        'fs_writes_bytes': 'bytes',
        'network_receive_bytes': 'bytes',
        'network_transmit_bytes': 'bytes',
        'block_cache_size': 'bytes',
        
        # 计数相关指标 - 统一使用个数
        'request': 'count',
        'response': 'count',
        'error': 'count',
        'timeout': 'count',
        'qps': 'count/s',
        'connection_count': 'count',
    }

    @classmethod
    def normalize_duration_to_microseconds(cls, value: Union[float, int], 
                                         source_unit: str = 'μs') -> float:
        """
        将时延值规范化为微秒
        
        Args:
            value: 原始时延值
            source_unit: 源单位 ('ns', 'μs', 'us', 'ms', 's')
            
        Returns:
            规范化后的微秒值
        """
        if value is None or value == 0:
            return 0.0
            
        source_unit = source_unit.lower()
        if source_unit not in cls.TIME_UNITS:
            logger.warning(f"未知时间单位: {source_unit}, 假设为微秒")
            return float(value)
            
        conversion_factor = cls.TIME_UNITS[source_unit]
        normalized_value = float(value) * conversion_factor
        
        logger.debug(f"时延规范化: {value}{source_unit} -> {normalized_value}μs")
        return normalized_value

    @classmethod
    def normalize_bytes(cls, value: Union[float, int], 
                       source_unit: str = 'bytes') -> float:
        """
        将字节值规范化为字节
        
        Args:
            value: 原始字节值
            source_unit: 源单位 ('B', 'KB', 'MB', 'GB', 'bytes')
            
        Returns:
            规范化后的字节值
        """
        if value is None or value == 0:
            return 0.0
            
        if source_unit not in cls.BYTE_UNITS:
            logger.warning(f"未知字节单位: {source_unit}, 假设为字节")
            return float(value)
            
        conversion_factor = cls.BYTE_UNITS[source_unit]
        normalized_value = float(value) * conversion_factor
        
        logger.debug(f"字节规范化: {value}{source_unit} -> {normalized_value}bytes")
        return normalized_value

    @classmethod
    def get_standard_unit(cls, metric_name: str) -> str:
        """
        获取指标的标准单位
        
        Args:
            metric_name: 指标名称
            
        Returns:
            标准单位字符串
        """
        # 精确匹配
        if metric_name in cls.METRIC_UNITS:
            return cls.METRIC_UNITS[metric_name]
            
        # 模糊匹配
        metric_lower = metric_name.lower()

        # 时延相关
        if any(keyword in metric_lower for keyword in ['rrt', 'duration', 'latency']):
            return 'μs'

        # 🔧 修复：字节相关优先于比例相关，避免memory_usage被错误识别为百分比
        # 字节相关 - 优先处理，避免与usage冲突
        if any(keyword in metric_lower for keyword in ['bytes', 'size', 'memory']):
            return 'bytes'

        # 比例相关 - 放在字节相关之后
        # 🔧 添加util关键词，支持io_util等指标
        if any(keyword in metric_lower for keyword in ['ratio', 'rate', 'usage', 'util']):
            return '%'
            
        # 计数相关
        if any(keyword in metric_lower for keyword in ['count', 'qps', 'request', 'response', 'error']):
            return 'count'
            
        return 'unknown'

    @classmethod
    def format_value_with_unit(cls, value: Union[float, int], 
                              metric_name: str, 
                              precision: int = 2) -> str:
        """
        格式化数值并添加标准单位
        
        Args:
            value: 数值
            metric_name: 指标名称
            precision: 小数精度
            
        Returns:
            格式化后的字符串
        """
        if value is None:
            return "N/A"
            
        unit = cls.get_standard_unit(metric_name)
        
        # 特殊格式化处理
        if unit == 'μs':
            # 时延值：显示微秒和毫秒两种单位便于阅读
            ms_value = value / 1000
            if ms_value >= 1000:
                return f"{ms_value/1000:.{precision}f}s ({value:.0f}μs)"
            elif ms_value >= 1:
                return f"{ms_value:.{precision}f}ms ({value:.0f}μs)"
            else:
                return f"{value:.{precision}f}μs"
                
        elif unit == 'bytes':
            # 字节值：自动选择合适的单位显示
            if value >= 1024**3:
                return f"{value/(1024**3):.{precision}f}GB ({value:.0f}bytes)"
            elif value >= 1024**2:
                return f"{value/(1024**2):.{precision}f}MB ({value:.0f}bytes)"
            elif value >= 1024:
                return f"{value/1024:.{precision}f}KB ({value:.0f}bytes)"
            else:
                return f"{value:.{precision}f}bytes"
                
        elif unit == '%':
            # 百分比：确保在0-100范围内
            if value > 1:
                # 如果值大于1，假设是百分比形式
                return f"{value:.{precision}f}%"
            else:
                # 如果值小于等于1，假设是小数形式，转换为百分比
                return f"{value*100:.{precision}f}%"
                
        elif unit == 'count' or unit == 'count/s':
            # 计数：使用整数显示
            return f"{value:.0f}"
            
        else:
            return f"{value:.{precision}f}"

    @classmethod
    def normalize_metric_data(cls, data: Dict[str, Any]) -> Dict[str, Any]:
        """
        规范化指标数据字典中的所有数值
        
        Args:
            data: 包含指标数据的字典
            
        Returns:
            规范化后的数据字典
        """
        normalized_data = data.copy()
        
        for key, value in data.items():
            if isinstance(value, (int, float)) and value is not None:
                # 根据指标名称进行单位规范化
                standard_unit = cls.get_standard_unit(key)
                
                if standard_unit == 'μs':
                    # 假设输入可能是毫秒，需要转换为微秒
                    # 这里需要根据实际数据源的单位进行调整
                    if 'rrt' in key.lower() and value < 100000:  # 如果rrt值小于100ms，可能是毫秒
                        normalized_data[key] = cls.normalize_duration_to_microseconds(value, 'ms')
                    else:
                        normalized_data[key] = cls.normalize_duration_to_microseconds(value, 'μs')
                        
        return normalized_data

    @classmethod
    def validate_unit_consistency(cls, metric_name: str, value: Union[float, int], 
                                 expected_unit: str) -> bool:
        """
        验证指标值的单位一致性
        
        Args:
            metric_name: 指标名称
            value: 指标值
            expected_unit: 期望的单位
            
        Returns:
            是否符合单位规范
        """
        standard_unit = cls.get_standard_unit(metric_name)
        
        if standard_unit != expected_unit:
            logger.warning(f"单位不一致: {metric_name} 期望单位 {expected_unit}, 标准单位 {standard_unit}")
            return False
            
        # 简单的数值范围检查
        if standard_unit == '%' and (value < 0 or value > 100):
            logger.warning(f"百分比值超出范围: {metric_name}={value}")
            return False
            
        if standard_unit == 'μs' and value < 0:
            logger.warning(f"时延值为负数: {metric_name}={value}")
            return False
            
        return True
