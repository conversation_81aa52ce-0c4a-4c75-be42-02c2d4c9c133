#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
智能阈值管理器
==============

功能特性:
1. 基于实际数据分布的阈值校准
2. 单位统一和自动转换
3. 动态自适应阈值调整
4. 多策略阈值计算
5. 异常检测逻辑优化

作者: AIOps团队
日期: 2025-07-26
"""

import json
import logging
import pandas as pd
import numpy as np
from typing import Dict, Any, Optional, Tuple
from pathlib import Path
from datetime import datetime, timedelta
import yaml

class IntelligentThresholdManager:
    """智能阈值管理器"""
    
    def __init__(self, config_path: str = None, calibration_data_path: str = None):
        """
        初始化智能阈值管理器
        
        Args:
            config_path: 配置文件路径
            calibration_data_path: 校准数据文件路径
        """
        self.logger = logging.getLogger(self.__class__.__name__)
        
        # 加载配置
        self.config = self._load_config(config_path)
        
        # 加载校准数据
        self.calibration_data = self._load_calibration_data(calibration_data_path)
        
        # 单位转换映射
        self.unit_converters = {
            'bytes_to_gb': lambda x: x / (1024**3),
            'bytes_to_mb': lambda x: x / (1024**2),
            'microseconds_to_ms': lambda x: x / 1000,
            'ratio_to_percentage': lambda x: x * 100,
            'percentage_to_ratio': lambda x: x / 100
        }
        
        # 阈值策略映射
        self.threshold_strategies = {
            'percentile_95': self._percentile_95_strategy,
            'percentile_90': self._percentile_90_strategy,
            'percentile_99': self._percentile_99_strategy,
            'percentile_95_plus': self._percentile_95_plus_strategy,
            'percentile_90_plus': self._percentile_90_plus_strategy,
            'percentile_05_reverse': self._percentile_05_reverse_strategy,
            'percentile_10_reverse': self._percentile_10_reverse_strategy,
            'statistical': self._statistical_strategy,
            'adaptive': self._adaptive_strategy
        }
        
        self.logger.info("✅ 智能阈值管理器初始化完成")
    
    def _load_config(self, config_path: str) -> Dict[str, Any]:
        """加载配置文件"""
        if config_path and Path(config_path).exists():
            with open(config_path, 'r', encoding='utf-8') as f:
                if config_path.endswith('.yaml') or config_path.endswith('.yml'):
                    return yaml.safe_load(f)
                else:
                    return json.load(f)
        
        # 默认配置
        return {
            'adaptive_window_hours': 24,
            'min_data_points': 100,
            'confidence_threshold': 0.8,
            'outlier_detection': True,
            'seasonal_adjustment': False
        }
    
    def _load_calibration_data(self, data_path: str) -> Dict[str, Any]:
        """加载校准数据"""
        if data_path and Path(data_path).exists():
            with open(data_path, 'r', encoding='utf-8') as f:
                return json.load(f)
        return {}
    
    def get_intelligent_threshold(self, component: str, metric: str, 
                                historical_data: pd.Series = None) -> Tuple[float, str, Dict[str, Any]]:
        """
        获取智能阈值
        
        Args:
            component: 组件名称 (tidb, tikv, pd)
            metric: 指标名称
            historical_data: 历史数据序列
            
        Returns:
            (threshold_value, threshold_type, metadata)
        """
        try:
            # 1. 尝试使用校准数据
            if component in self.calibration_data and metric in self.calibration_data[component]:
                calibrated = self.calibration_data[component][metric]
                threshold = calibrated['threshold']
                strategy = calibrated.get('strategy', 'data_driven')
                unit = calibrated.get('unit', calibrated.get('unit_detected', 'unknown'))
                
                # 2. 如果有历史数据，进行动态调整
                if historical_data is not None and len(historical_data) >= self.config['min_data_points']:
                    threshold = self._apply_adaptive_adjustment(threshold, historical_data, strategy)
                
                # 3. 单位统一处理
                threshold, unit = self._normalize_unit(threshold, unit, metric)
                
                metadata = {
                    'source': 'calibrated',
                    'strategy': strategy,
                    'unit': unit,
                    'confidence': 0.9,
                    'data_points': len(historical_data) if historical_data is not None else 0
                }
                
                return threshold, 'intelligent', metadata
            
            # 4. 如果没有校准数据，使用历史数据计算
            elif historical_data is not None and len(historical_data) >= self.config['min_data_points']:
                return self._calculate_threshold_from_data(component, metric, historical_data)
            
            # 5. 使用fallback阈值
            else:
                return self._get_fallback_threshold(component, metric)
                
        except Exception as e:
            self.logger.warning(f"智能阈值计算失败 {component}.{metric}: {e}")
            return self._get_fallback_threshold(component, metric)
    
    def _apply_adaptive_adjustment(self, base_threshold: float, data: pd.Series, strategy: str) -> float:
        """应用自适应调整"""
        try:
            # 检测数据趋势
            recent_data = data.tail(min(100, len(data)))
            trend = self._detect_trend(recent_data)
            
            # 检测异常值
            if self.config['outlier_detection']:
                data = self._remove_outliers(data)
            
            # 根据趋势调整阈值
            if trend > 0.1:  # 上升趋势
                adjustment_factor = 1.1
            elif trend < -0.1:  # 下降趋势
                adjustment_factor = 0.95
            else:  # 稳定
                adjustment_factor = 1.0
            
            # 应用策略特定的调整
            if 'reverse' in strategy:
                adjustment_factor = 1 / adjustment_factor
            
            return base_threshold * adjustment_factor
            
        except Exception as e:
            self.logger.debug(f"自适应调整失败: {e}")
            return base_threshold
    
    def _detect_trend(self, data: pd.Series) -> float:
        """检测数据趋势"""
        if len(data) < 10:
            return 0.0
        
        try:
            x = np.arange(len(data))
            y = data.values
            
            # 简单线性回归
            slope = np.polyfit(x, y, 1)[0]
            
            # 标准化斜率
            data_range = data.max() - data.min()
            if data_range > 0:
                normalized_slope = slope / data_range
            else:
                normalized_slope = 0.0
            
            return normalized_slope
            
        except Exception:
            return 0.0
    
    def _remove_outliers(self, data: pd.Series) -> pd.Series:
        """移除异常值"""
        try:
            Q1 = data.quantile(0.25)
            Q3 = data.quantile(0.75)
            IQR = Q3 - Q1
            
            lower_bound = Q1 - 1.5 * IQR
            upper_bound = Q3 + 1.5 * IQR
            
            return data[(data >= lower_bound) & (data <= upper_bound)]
            
        except Exception:
            return data
    
    def _normalize_unit(self, threshold: float, unit: str, metric: str) -> Tuple[float, str]:
        """单位统一处理"""
        try:
            # 内存相关指标统一为字节
            if 'memory' in metric or 'cache' in metric:
                if unit == 'unknown' and threshold < 1000:  # 可能是GB
                    threshold = threshold * (1024**3)
                    unit = 'bytes'
                elif unit == 'bytes_gb':
                    unit = 'bytes'
                elif unit == 'bytes_mb':
                    threshold = threshold * 1024
                    unit = 'bytes'
            
            # CPU使用率统一为比例 (0-1)
            elif 'cpu' in metric or 'usage' in metric:
                if unit == 'percentage' or threshold > 1:
                    threshold = threshold / 100
                    unit = 'ratio'
            
            # 延迟相关指标统一为微秒
            elif 'duration' in metric or 'latency' in metric or 'wait' in metric:
                if unit == 'milliseconds':
                    threshold = threshold * 1000
                    unit = 'microseconds'
                elif unit == 'seconds':
                    threshold = threshold * 1000000
                    unit = 'microseconds'
            
            # 存储大小统一为字节
            elif 'size' in metric or 'capacity' in metric:
                if unit == 'bytes_gb':
                    unit = 'bytes'
                elif unit == 'bytes_mb':
                    threshold = threshold * 1024
                    unit = 'bytes'
            
            return threshold, unit

        except Exception as e:
            self.logger.debug(f"单位统一失败: {e}")
            return threshold, unit

    def _calculate_threshold_from_data(self, component: str, metric: str,
                                     data: pd.Series) -> Tuple[float, str, Dict[str, Any]]:
        """从历史数据计算阈值"""
        try:
            # 清理数据
            clean_data = self._remove_outliers(data) if self.config['outlier_detection'] else data

            # 根据指标类型选择策略
            strategy = self._determine_strategy(metric)

            # 计算阈值
            threshold_func = self.threshold_strategies.get(strategy, self._percentile_95_strategy)
            threshold = threshold_func(clean_data)

            # 检测单位
            unit = self._detect_unit_from_data(metric, clean_data)

            # 单位统一
            threshold, unit = self._normalize_unit(threshold, unit, metric)

            metadata = {
                'source': 'calculated',
                'strategy': strategy,
                'unit': unit,
                'confidence': 0.7,
                'data_points': len(clean_data)
            }

            return threshold, 'calculated', metadata

        except Exception as e:
            self.logger.warning(f"数据计算阈值失败 {component}.{metric}: {e}")
            return self._get_fallback_threshold(component, metric)

    def _determine_strategy(self, metric: str) -> str:
        """根据指标名称确定阈值策略"""
        if 'usage' in metric or 'util' in metric:
            return 'percentile_95'
        elif 'health' in metric or 'up_count' in metric or 'leader_count' in metric:
            return 'percentile_05_reverse'
        elif 'capacity' in metric or 'available' in metric:
            return 'percentile_10_reverse'
        elif 'duration' in metric or 'latency' in metric:
            return 'percentile_90_plus'
        elif 'mbps' in metric or 'throughput' in metric:
            return 'percentile_99'
        elif 'qps' in metric or 'ops' in metric:
            return 'percentile_95_plus'
        else:
            return 'percentile_95'

    def _detect_unit_from_data(self, metric: str, data: pd.Series) -> str:
        """从数据检测单位"""
        mean_val = data.mean()
        max_val = data.max()

        if 'usage' in metric and max_val <= 1:
            return 'ratio'
        elif 'usage' in metric and max_val <= 100:
            return 'percentage'
        elif 'bytes' in metric or 'size' in metric or 'memory' in metric:
            if mean_val > 1024**3:
                return 'bytes'
            elif mean_val > 1024**2:
                return 'bytes_mb'
            else:
                return 'bytes'
        elif 'duration' in metric or 'latency' in metric:
            if mean_val > 1000000:
                return 'microseconds'
            elif mean_val > 1000:
                return 'milliseconds'
            else:
                return 'seconds'
        else:
            return 'unknown'

    def _get_fallback_threshold(self, component: str, metric: str) -> Tuple[float, str, Dict[str, Any]]:
        """获取fallback阈值"""
        fallback_thresholds = {
            'tidb': {
                'qps': 1000, 'duration_99th': 1000000, 'duration_95th': 500000,
                'failed_query_ops': 10, 'connection_count': 100, 'memory_usage': 2*1024**3,
                'cpu_usage': 0.8, 'server_is_up': 0.5
            },
            'tikv': {
                'cpu_usage': 0.8, 'memory_usage': 2*1024**3, 'qps': 5000,
                'io_util': 0.8, 'available_size': 10*1024**3, 'server_is_up': 0.5
            },
            'pd': {
                'cpu_usage': 0.8, 'memory_usage': 1024**3, 'abnormal_region_count': 10,
                'store_down_count': 0, 'region_health': 50, 'store_up_count': 1
            }
        }

        threshold = fallback_thresholds.get(component, {}).get(metric, 100)

        metadata = {
            'source': 'fallback',
            'strategy': 'fixed',
            'unit': 'unknown',
            'confidence': 0.5,
            'data_points': 0
        }

        return threshold, 'fallback', metadata

    # 阈值策略实现
    def _percentile_95_strategy(self, data: pd.Series) -> float:
        return data.quantile(0.95) * 1.1

    def _percentile_90_strategy(self, data: pd.Series) -> float:
        return data.quantile(0.90) * 1.2

    def _percentile_99_strategy(self, data: pd.Series) -> float:
        return data.quantile(0.99) * 1.05

    def _percentile_95_plus_strategy(self, data: pd.Series) -> float:
        return data.quantile(0.95) * 1.2

    def _percentile_90_plus_strategy(self, data: pd.Series) -> float:
        return data.quantile(0.90) * 1.3

    def _percentile_05_reverse_strategy(self, data: pd.Series) -> float:
        return data.quantile(0.05) * 0.8

    def _percentile_10_reverse_strategy(self, data: pd.Series) -> float:
        return data.quantile(0.10) * 0.7

    def _statistical_strategy(self, data: pd.Series) -> float:
        mean = data.mean()
        std = data.std()
        return mean + 2 * std

    def _adaptive_strategy(self, data: pd.Series) -> float:
        # 结合多种策略的自适应方法
        p95 = self._percentile_95_strategy(data)
        stat = self._statistical_strategy(data)
        return (p95 + stat) / 2

    def is_anomaly(self, component: str, metric: str, value: float,
                   threshold: float, threshold_type: str, metadata: Dict[str, Any]) -> bool:
        """判断是否为异常"""
        try:
            strategy = metadata.get('strategy', 'percentile_95')

            # 反向逻辑指标
            if 'reverse' in strategy or 'health' in metric or 'up_count' in metric or \
               'capacity' in metric or 'available' in metric or 'leader_count' in metric:
                return value < threshold
            else:
                return value > threshold

        except Exception as e:
            self.logger.debug(f"异常判断失败: {e}")
            return value > threshold

    def get_threshold_explanation(self, component: str, metric: str,
                                threshold: float, metadata: Dict[str, Any]) -> str:
        """获取阈值解释"""
        source = metadata.get('source', 'unknown')
        strategy = metadata.get('strategy', 'unknown')
        confidence = metadata.get('confidence', 0.5)
        data_points = metadata.get('data_points', 0)

        if source == 'calibrated':
            return f"基于{data_points}个历史数据点校准，使用{strategy}策略，置信度{confidence:.1%}"
        elif source == 'calculated':
            return f"基于{data_points}个数据点实时计算，使用{strategy}策略，置信度{confidence:.1%}"
        else:
            return f"使用fallback阈值，置信度{confidence:.1%}"
