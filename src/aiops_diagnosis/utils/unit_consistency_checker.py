#!/usr/bin/env python3
"""
单位一致性检查工具
验证整个AIOps系统中的数据单位是否符合DeepFlow官方文档规范

作者: AI Assistant
日期: 2025-07-24
"""

import os
import re
import json
import yaml
import logging
from typing import Dict, List, Tuple, Any
from pathlib import Path

from .unit_normalizer import UnitNormalizer

logger = logging.getLogger(__name__)


class UnitConsistencyChecker:
    """单位一致性检查器"""
    
    def __init__(self, project_root: str):
        self.project_root = Path(project_root)
        self.issues = []
        
    def check_all(self) -> Dict[str, Any]:
        """执行全面的单位一致性检查"""
        logger.info("🔍 开始单位一致性检查...")
        
        results = {
            "config_files": self._check_config_files(),
            "agent_code": self._check_agent_code(),
            "threshold_definitions": self._check_threshold_definitions(),
            "summary": self._generate_summary()
        }
        
        return results
    
    def _check_config_files(self) -> Dict[str, Any]:
        """检查配置文件中的单位定义"""
        logger.info("📋 检查配置文件...")
        
        config_issues = []
        config_dir = self.project_root / "aiops_diagnosis" / "config"
        
        # 检查metric_thresholds.json
        threshold_file = config_dir / "metric_thresholds.json"
        if threshold_file.exists():
            try:
                with open(threshold_file, 'r', encoding='utf-8') as f:
                    config = json.load(f)
                
                # 检查APM指标单位
                apm_metrics = config.get("fault_thresholds", {}).get("apm_metrics", {})
                for metric_name, metric_config in apm_metrics.items():
                    expected_unit = UnitNormalizer.get_standard_unit(metric_name)
                    actual_unit = metric_config.get("unit", "unknown")
                    
                    if expected_unit != actual_unit and expected_unit != "unknown":
                        config_issues.append({
                            "file": str(threshold_file),
                            "metric": metric_name,
                            "expected_unit": expected_unit,
                            "actual_unit": actual_unit,
                            "issue": f"单位不匹配: 期望 {expected_unit}, 实际 {actual_unit}"
                        })
                        
            except Exception as e:
                config_issues.append({
                    "file": str(threshold_file),
                    "issue": f"文件解析错误: {e}"
                })
        
        return {
            "checked_files": [str(threshold_file)],
            "issues": config_issues,
            "total_issues": len(config_issues)
        }
    
    def _check_agent_code(self) -> Dict[str, Any]:
        """检查Agent代码中的单位使用"""
        logger.info("🤖 检查Agent代码...")
        
        code_issues = []
        agents_dir = self.project_root / "aiops_diagnosis" / "autogen_agents"
        
        # 需要检查的模式
        patterns = {
            "duration_ms": r"duration.*ms|duration.*毫秒",
            "rrt_ms": r"rrt.*ms|rrt.*毫秒", 
            "hardcoded_thresholds": r"(rrt|duration).*[><=]\s*\d+\s*(?!000000)",  # 检查非微秒阈值
            "unit_comments": r"#.*毫秒|#.*ms(?!ec)|#.*秒(?!微)",  # 检查注释中的错误单位
        }
        
        for agent_file in agents_dir.glob("*.py"):
            try:
                with open(agent_file, 'r', encoding='utf-8') as f:
                    content = f.read()
                    
                for pattern_name, pattern in patterns.items():
                    matches = re.finditer(pattern, content, re.IGNORECASE)
                    for match in matches:
                        line_num = content[:match.start()].count('\n') + 1
                        code_issues.append({
                            "file": str(agent_file),
                            "line": line_num,
                            "pattern": pattern_name,
                            "match": match.group(),
                            "issue": f"可能的单位不一致: {match.group()}"
                        })
                        
            except Exception as e:
                code_issues.append({
                    "file": str(agent_file),
                    "issue": f"文件读取错误: {e}"
                })
        
        return {
            "checked_files": [str(f) for f in agents_dir.glob("*.py")],
            "issues": code_issues,
            "total_issues": len(code_issues)
        }
    
    def _check_threshold_definitions(self) -> Dict[str, Any]:
        """检查阈值定义的合理性"""
        logger.info("⚖️ 检查阈值定义...")
        
        threshold_issues = []
        
        # 检查常见的时延阈值是否在合理范围内
        time_thresholds = {
            "rrt": (100000, 10000000),      # 100ms - 10s (微秒)
            "rrt_max": (500000, 30000000),  # 500ms - 30s (微秒)
            "duration": (1000, 60000000),   # 1ms - 60s (微秒)
        }
        
        # 这里可以添加更多的阈值合理性检查逻辑
        # 例如从配置文件中读取阈值并验证
        
        return {
            "checked_thresholds": list(time_thresholds.keys()),
            "issues": threshold_issues,
            "total_issues": len(threshold_issues)
        }
    
    def _generate_summary(self) -> Dict[str, Any]:
        """生成检查摘要"""
        total_issues = sum([
            len(self.issues) for issues in [
                self._check_config_files().get("issues", []),
                self._check_agent_code().get("issues", []),
                self._check_threshold_definitions().get("issues", [])
            ]
        ])
        
        return {
            "total_issues": total_issues,
            "status": "PASS" if total_issues == 0 else "FAIL",
            "recommendations": self._get_recommendations()
        }
    
    def _get_recommendations(self) -> List[str]:
        """获取修复建议"""
        recommendations = [
            "确保所有时延相关指标(rrt, duration)使用微秒(μs)作为单位",
            "确保所有比例相关指标使用百分比(%)作为单位", 
            "确保所有字节相关指标使用字节(bytes)作为单位",
            "使用UnitNormalizer类进行统一的单位管理",
            "在配置文件中明确标注单位信息",
            "在代码注释中使用正确的单位描述"
        ]
        
        return recommendations
    
    def print_report(self, results: Dict[str, Any]):
        """打印检查报告"""
        print("\n" + "="*60)
        print("🔍 AIOps系统单位一致性检查报告")
        print("="*60)
        
        # 配置文件检查结果
        config_results = results["config_files"]
        print(f"\n📋 配置文件检查:")
        print(f"   检查文件数: {len(config_results['checked_files'])}")
        print(f"   发现问题数: {config_results['total_issues']}")
        
        if config_results["issues"]:
            print("   问题详情:")
            for issue in config_results["issues"]:
                print(f"     - {issue['issue']}")
        
        # Agent代码检查结果
        code_results = results["agent_code"]
        print(f"\n🤖 Agent代码检查:")
        print(f"   检查文件数: {len(code_results['checked_files'])}")
        print(f"   发现问题数: {code_results['total_issues']}")
        
        if code_results["issues"]:
            print("   问题详情:")
            for issue in code_results["issues"][:5]:  # 只显示前5个
                print(f"     - {issue['file']}:{issue['line']} - {issue['issue']}")
            if len(code_results["issues"]) > 5:
                print(f"     ... 还有 {len(code_results['issues']) - 5} 个问题")
        
        # 阈值定义检查结果
        threshold_results = results["threshold_definitions"]
        print(f"\n⚖️ 阈值定义检查:")
        print(f"   检查阈值数: {len(threshold_results['checked_thresholds'])}")
        print(f"   发现问题数: {threshold_results['total_issues']}")
        
        # 总结
        summary = results["summary"]
        print(f"\n📊 检查总结:")
        print(f"   总问题数: {summary['total_issues']}")
        print(f"   检查状态: {summary['status']}")
        
        if summary["status"] == "FAIL":
            print(f"\n💡 修复建议:")
            for rec in summary["recommendations"]:
                print(f"   - {rec}")
        
        print("\n" + "="*60)


def main():
    """主函数"""
    import sys
    
    # 获取项目根目录
    if len(sys.argv) > 1:
        project_root = sys.argv[1]
    else:
        project_root = os.path.dirname(os.path.dirname(os.path.dirname(__file__)))
    
    # 执行检查
    checker = UnitConsistencyChecker(project_root)
    results = checker.check_all()
    checker.print_report(results)
    
    # 返回退出码
    return 0 if results["summary"]["status"] == "PASS" else 1


if __name__ == "__main__":
    import sys
    sys.exit(main())
