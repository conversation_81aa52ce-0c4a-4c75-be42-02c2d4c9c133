"""
多维度自适应阈值管理器 - 专门针对TiDB集群的多维度异常检测
"""

import pandas as pd
import numpy as np
import os
import json
import yaml
from datetime import datetime, timedelta
from typing import Dict, Any, Optional, List, Tuple
import logging
from pathlib import Path
# 移除sklearn依赖，使用简单的统计方法
import warnings
warnings.filterwarnings('ignore')


class MultiDimensionalThresholdManager:
    """多维度自适应阈值管理器"""
    
    def __init__(self, data_root: str = "/data/data", 
                 cache_dir: str = "multidim_threshold_cache",
                 lookback_days: int = 7):
        self.data_root = data_root
        self.cache_dir = Path(cache_dir)
        self.cache_dir.mkdir(exist_ok=True)
        self.lookback_days = lookback_days
        self.logger = logging.getLogger(__name__)
        
        # TiDB集群组件的多维度指标定义
        self.tidb_cluster_metrics = {
            'tidb': {
                'performance': ['qps', 'duration_avg', 'duration_95th', 'duration_99th'],
                'resource': ['connection_count', 'block_cache_size'],
                'error': ['failed_query_ops', 'slow_query']
            },
            'pd': {
                'resource': ['cpu_usage', 'memory_usage'],
                'cluster_health': ['abnormal_region_count', 'region_health', 'region_count'],
                'store_management': ['store_up_count', 'store_down_count', 'store_unhealth_count', 'store_slow_count', 'store_low_space_count'],
                'capacity': ['storage_size', 'storage_capacity', 'storage_used_ratio'],
                'leadership': ['leader_count', 'learner_count', 'witness_count']
            },
            'tikv': {
                'resource': ['memory_usage', 'cpu_usage', 'threadpool_readpool_cpu'],
                'storage': ['available_size', 'capacity_size', 'store_size'],
                'performance': ['qps', 'grpc_qps', 'raft_propose_wait', 'raft_apply_wait'],
                'io': ['io_util', 'read_mbps', 'write_wal_mbps'],
                'cluster': ['region_pending', 'snapshot_apply_count', 'server_is_up'],
                'engine': ['rocksdb_write_stall']
            }
        }
        
        # 多维度相关性规则
        self.correlation_rules = {
            'tidb_performance_degradation': {
                'primary_metrics': ['duration_99th', 'duration_95th'],
                'secondary_metrics': ['qps', 'connection_count'],
                'correlation_threshold': 0.7,
                'description': 'TiDB性能下降：延迟增加通常伴随QPS下降或连接数异常'
            },
            'tikv_resource_pressure': {
                'primary_metrics': ['memory_usage', 'cpu_usage'],
                'secondary_metrics': ['qps', 'raft_propose_wait'],
                'correlation_threshold': 0.6,
                'description': 'TiKV资源压力：资源使用率高时通常伴随请求处理延迟'
            },
            'pd_cluster_instability': {
                'primary_metrics': ['abnormal_region_count'],
                'secondary_metrics': ['store_down_count', 'store_unhealth_count'],
                'correlation_threshold': 0.5,
                'description': 'PD集群不稳定：异常Region增加通常伴随存储节点问题'
            }
        }
    
    def get_multidimensional_threshold(self, component_type: str, current_time: str) -> Dict[str, Any]:
        """获取多维度阈值"""
        try:
            # 1. 检查缓存
            cache_key = f"{component_type}_{current_time[:10]}"  # 按天缓存
            cached_result = self._get_cached_multidim_threshold(cache_key)
            if cached_result:
                return cached_result
            
            # 2. 加载历史数据
            historical_data = self._load_component_historical_data(component_type, current_time)
            
            if historical_data.empty:
                self.logger.warning(f"无{component_type}历史数据，使用默认阈值")
                return self._get_default_multidim_threshold(component_type)
            
            # 3. 计算多维度阈值
            result = self._calculate_multidimensional_threshold(component_type, historical_data)
            
            # 4. 缓存结果
            self._cache_multidim_threshold(cache_key, result)
            
            return result
            
        except Exception as e:
            self.logger.error(f"获取{component_type}多维度阈值失败: {e}")
            return self._get_default_multidim_threshold(component_type)
    
    def _load_component_historical_data(self, component_type: str, current_time: str) -> pd.DataFrame:
        """加载组件的所有历史数据"""
        try:
            current_dt = pd.to_datetime(current_time)
            all_data = []
            
            # 获取该组件的所有指标
            if component_type not in self.tidb_cluster_metrics:
                return pd.DataFrame()
            
            component_metrics = self.tidb_cluster_metrics[component_type]
            all_metric_names = []
            for category, metrics in component_metrics.items():
                all_metric_names.extend(metrics)
            
            # 加载过去N天的数据
            for i in range(1, self.lookback_days + 1):
                target_date = current_dt - timedelta(days=i)
                date_str = target_date.strftime('%Y-%m-%d')
                
                daily_data = {}
                
                # 加载每个指标的数据
                for metric_name in all_metric_names:
                    if component_type == 'tidb':
                        file_path = os.path.join(
                            self.data_root, date_str, "metric-parquet", "infra", 
                            f"infra_tidb_{metric_name}_{date_str}.parquet"
                        )
                    else:  # pd, tikv
                        file_path = os.path.join(
                            self.data_root, date_str, "metric-parquet", "other",
                            f"infra_{component_type}_{metric_name}_{date_str}.parquet"
                        )
                    
                    if os.path.exists(file_path):
                        try:
                            df = pd.read_parquet(file_path)
                            if not df.empty:
                                # 提取数值列
                                value_cols = [col for col in df.columns if col in [metric_name, 'value', 'metric_value']]
                                if value_cols:
                                    values = df[value_cols[0]].dropna()
                                    if not values.empty:
                                        daily_data[metric_name] = values.mean()  # 使用日均值
                        except Exception as e:
                            self.logger.debug(f"加载{file_path}失败: {e}")
                
                if daily_data:
                    daily_data['date'] = date_str
                    all_data.append(daily_data)
            
            if all_data:
                return pd.DataFrame(all_data)
            else:
                return pd.DataFrame()
                
        except Exception as e:
            self.logger.error(f"加载{component_type}历史数据失败: {e}")
            return pd.DataFrame()
    
    def _calculate_multidimensional_threshold(self, component_type: str, data: pd.DataFrame) -> Dict[str, Any]:
        """计算多维度阈值"""
        result = {
            'component_type': component_type,
            'single_metric_thresholds': {},
            'correlation_thresholds': {},
            'anomaly_detection_model': None,
            'data_statistics': {}
        }
        
        try:
            # 1. 计算单指标阈值
            for column in data.columns:
                if column != 'date' and data[column].notna().sum() > 3:
                    values = data[column].dropna()
                    
                    # 使用95分位数作为阈值
                    threshold = values.quantile(0.95)
                    
                    # 应用最小阈值保护
                    threshold = self._apply_min_threshold_protection(column, threshold)
                    
                    result['single_metric_thresholds'][column] = {
                        'threshold': threshold,
                        'mean': values.mean(),
                        'std': values.std(),
                        'min': values.min(),
                        'max': values.max(),
                        'data_points': len(values)
                    }
            
            # 2. 计算指标间相关性
            numeric_columns = [col for col in data.columns if col != 'date']
            if len(numeric_columns) >= 2:
                correlation_matrix = data[numeric_columns].corr()
                result['correlation_matrix'] = correlation_matrix.to_dict()
                
                # 识别强相关指标对
                strong_correlations = []
                for i, col1 in enumerate(numeric_columns):
                    for j, col2 in enumerate(numeric_columns[i+1:], i+1):
                        corr_value = correlation_matrix.loc[col1, col2]
                        if abs(corr_value) > 0.7:  # 强相关阈值
                            strong_correlations.append({
                                'metric1': col1,
                                'metric2': col2,
                                'correlation': corr_value
                            })
                
                result['strong_correlations'] = strong_correlations
            
            # 3. 简化的异常检测模型（基于统计方法）
            if len(numeric_columns) >= 2 and len(data) >= 5:
                try:
                    # 计算每个指标的统计特征
                    stats_model = {}
                    for col in numeric_columns:
                        col_data = data[col].dropna()
                        if len(col_data) > 0:
                            stats_model[col] = {
                                'mean': col_data.mean(),
                                'std': col_data.std(),
                                'q95': col_data.quantile(0.95),
                                'q05': col_data.quantile(0.05)
                            }

                    result['anomaly_detection_model'] = {
                        'type': 'statistical',
                        'feature_stats': stats_model,
                        'feature_names': numeric_columns
                    }

                except Exception as e:
                    self.logger.debug(f"构建统计模型失败: {e}")
            
            # 4. 应用相关性规则
            for rule_name, rule_config in self.correlation_rules.items():
                if component_type in rule_name:
                    primary_metrics = rule_config['primary_metrics']
                    secondary_metrics = rule_config['secondary_metrics']
                    
                    # 检查是否有足够的指标数据
                    available_primary = [m for m in primary_metrics if m in result['single_metric_thresholds']]
                    available_secondary = [m for m in secondary_metrics if m in result['single_metric_thresholds']]
                    
                    if available_primary and available_secondary:
                        result['correlation_thresholds'][rule_name] = {
                            'primary_metrics': available_primary,
                            'secondary_metrics': available_secondary,
                            'rule': rule_config,
                            'enabled': True
                        }
            
            self.logger.info(f"✅ {component_type}多维度阈值计算完成")
            self.logger.info(f"   单指标阈值: {len(result['single_metric_thresholds'])}个")
            self.logger.info(f"   相关性规则: {len(result['correlation_thresholds'])}个")
            
            return result
            
        except Exception as e:
            self.logger.error(f"计算{component_type}多维度阈值失败: {e}")
            return self._get_default_multidim_threshold(component_type)
    
    def _apply_min_threshold_protection(self, metric_name: str, threshold: float) -> float:
        """应用最小阈值保护"""
        metric_lower = metric_name.lower()
        
        if 'cpu' in metric_lower and 'usage' in metric_lower:
            return max(threshold, 0.8)  # CPU使用率最少80%
        elif 'memory' in metric_lower and 'usage' in metric_lower:
            return max(threshold, 1024*1024*1024)  # 内存使用最少1GB
        elif 'qps' in metric_lower:
            return max(threshold, 100)  # QPS最少100
        elif 'duration' in metric_lower or 'wait' in metric_lower:
            return max(threshold, 100000)  # 延迟最少100ms (微秒)
        elif 'count' in metric_lower:
            return max(threshold, 10)  # 计数类最少10
        else:
            return threshold
    
    def detect_multidimensional_anomaly(self, component_type: str, current_metrics: Dict[str, float], 
                                       current_time: str) -> Dict[str, Any]:
        """多维度异常检测"""
        try:
            # 获取多维度阈值
            thresholds = self.get_multidimensional_threshold(component_type, current_time)
            
            result = {
                'component_type': component_type,
                'single_metric_anomalies': [],
                'correlation_anomalies': [],
                'multidimensional_anomaly_score': 0.0,
                'overall_anomaly': False
            }
            
            # 1. 单指标异常检测
            single_anomaly_count = 0
            for metric_name, current_value in current_metrics.items():
                if metric_name in thresholds['single_metric_thresholds']:
                    threshold_info = thresholds['single_metric_thresholds'][metric_name]
                    threshold = threshold_info['threshold']

                    # 🔧 调试：记录阈值使用情况
                    self.logger.debug(f"🔍 阈值检查: {metric_name}={current_value:.2f}, 阈值={threshold:.2f}")

                    if current_value > threshold:
                        deviation = current_value / threshold if threshold > 0 else float('inf')

                        # 🔧 移除错误的过滤逻辑，保留真实的异常值
                        # 不再过滤高偏差值，通过正确配置阈值来解决问题

                        # 🔧 调试：记录合理的异常偏差值
                        if deviation > 1000:
                            self.logger.warning(f"⚠️ 高偏差值: {metric_name}={current_value:.2f}, 阈值={threshold:.2f}, 偏差={deviation:.1f}x")
                        result['single_metric_anomalies'].append({
                            'metric': metric_name,
                            'value': current_value,
                            'threshold': threshold,
                            'deviation': deviation,
                            'severity': self._calculate_severity(deviation)
                        })
                        single_anomaly_count += 1
            
            # 2. 相关性异常检测
            for rule_name, rule_config in thresholds['correlation_thresholds'].items():
                if rule_config['enabled']:
                    correlation_anomaly = self._detect_correlation_anomaly(
                        rule_config, current_metrics, thresholds['single_metric_thresholds']
                    )
                    if correlation_anomaly:
                        result['correlation_anomalies'].append(correlation_anomaly)
            
            # 3. 计算综合异常分数
            single_score = min(single_anomaly_count / len(current_metrics), 1.0) * 0.6
            correlation_score = min(len(result['correlation_anomalies']) / max(len(thresholds['correlation_thresholds']), 1), 1.0) * 0.4
            
            result['multidimensional_anomaly_score'] = single_score + correlation_score
            result['overall_anomaly'] = result['multidimensional_anomaly_score'] > 0.3  # 30%阈值
            
            return result
            
        except Exception as e:
            self.logger.error(f"多维度异常检测失败: {e}")
            return {'component_type': component_type, 'overall_anomaly': False, 'error': str(e)}
    
    def _detect_correlation_anomaly(self, rule_config: Dict[str, Any], current_metrics: Dict[str, float], 
                                   single_thresholds: Dict[str, Any]) -> Optional[Dict[str, Any]]:
        """检测相关性异常"""
        try:
            primary_metrics = rule_config['primary_metrics']
            secondary_metrics = rule_config['secondary_metrics']
            
            # 检查主要指标是否异常
            primary_anomalies = []
            for metric in primary_metrics:
                if metric in current_metrics and metric in single_thresholds:
                    current_value = current_metrics[metric]
                    threshold = single_thresholds[metric]['threshold']
                    if current_value > threshold:
                        primary_anomalies.append(metric)
            
            # 如果主要指标异常，检查次要指标
            if primary_anomalies:
                secondary_anomalies = []
                for metric in secondary_metrics:
                    if metric in current_metrics and metric in single_thresholds:
                        current_value = current_metrics[metric]
                        threshold = single_thresholds[metric]['threshold']
                        if current_value > threshold:
                            secondary_anomalies.append(metric)
                
                # 如果次要指标也异常，则认为是相关性异常
                if secondary_anomalies:
                    return {
                        'rule_name': rule_config['rule']['description'],
                        'primary_anomalies': primary_anomalies,
                        'secondary_anomalies': secondary_anomalies,
                        'confidence': len(secondary_anomalies) / len(secondary_metrics)
                    }
            
            return None

        except Exception as e:
            self.logger.debug(f"相关性异常检测失败: {e}")
            return None

    def _calculate_severity(self, deviation: float) -> str:
        """计算异常严重程度"""
        if deviation >= 5.0:
            return 'critical'
        elif deviation >= 3.0:
            return 'high'
        elif deviation >= 2.0:
            return 'medium'
        else:
            return 'low'

    def _get_cached_multidim_threshold(self, cache_key: str) -> Optional[Dict[str, Any]]:
        """获取缓存的多维度阈值"""
        try:
            cache_file = self.cache_dir / f"{cache_key}.json"
            if not cache_file.exists():
                return None

            with open(cache_file, 'r') as f:
                cache_data = json.load(f)

            # 检查缓存是否过期（24小时）
            cache_time = pd.to_datetime(cache_data['timestamp'])
            current_time = pd.Timestamp.now()

            if (current_time - cache_time).total_seconds() < 86400:  # 24小时内有效
                return cache_data['thresholds']

            return None

        except Exception as e:
            self.logger.debug(f"读取多维度缓存失败: {e}")
            return None

    def _cache_multidim_threshold(self, cache_key: str, thresholds: Dict[str, Any]):
        """缓存多维度阈值"""
        try:
            cache_file = self.cache_dir / f"{cache_key}.json"
            cache_data = {
                'cache_key': cache_key,
                'thresholds': thresholds,
                'timestamp': pd.Timestamp.now().isoformat()
            }

            with open(cache_file, 'w') as f:
                json.dump(cache_data, f, indent=2, default=str)

        except Exception as e:
            self.logger.debug(f"缓存多维度阈值失败: {e}")

    def _get_default_multidim_threshold(self, component_type: str) -> Dict[str, Any]:
        """获取默认多维度阈值"""
        defaults = {
            'tidb': {
                'single_metric_thresholds': {
                    'connection_count': {'threshold': 1000, 'mean': 500, 'std': 200},
                    'qps': {'threshold': 5000, 'mean': 2000, 'std': 1000},
                    'duration_avg': {'threshold': 100000, 'mean': 50000, 'std': 20000},  # 100ms
                    'duration_95th': {'threshold': 500000, 'mean': 200000, 'std': 100000},  # 500ms
                    'duration_99th': {'threshold': 1000000, 'mean': 400000, 'std': 200000},  # 1s
                    'failed_query_ops': {'threshold': 100, 'mean': 10, 'std': 20},
                    'slow_query': {'threshold': 50, 'mean': 5, 'std': 10},
                    'block_cache_size': {'threshold': 2*1024*1024*1024, 'mean': 1024*1024*1024, 'std': 500*1024*1024}
                }
            },
            'pd': {
                'single_metric_thresholds': {
                    'abnormal_region_count': {'threshold': 100, 'mean': 10, 'std': 20},
                    'region_health': {'threshold': 0.95, 'mean': 0.99, 'std': 0.02},
                    'store_up_count': {'threshold': 3, 'mean': 3, 'std': 0},
                    'store_down_count': {'threshold': 1, 'mean': 0, 'std': 0},
                    'store_unhealth_count': {'threshold': 1, 'mean': 0, 'std': 0},
                    # 🔧 修复：基于实际数据的阈值 (8.02GB)
                    'storage_size': {'threshold': **********.0, 'mean': **********.0, 'std': **********.0},
                    'region_count': {'threshold': 250, 'mean': 150, 'std': 50}
                }
            },
            'tikv': {
                'single_metric_thresholds': {
                    'memory_usage': {'threshold': 2*1024*1024*1024, 'mean': 1024*1024*1024, 'std': 500*1024*1024},
                    'cpu_usage': {'threshold': 0.8, 'mean': 0.4, 'std': 0.2},
                    'qps': {'threshold': 5000, 'mean': 2000, 'std': 1000},
                    'grpc_qps': {'threshold': 1000, 'mean': 500, 'std': 200},
                    'raft_propose_wait': {'threshold': 50000, 'mean': 10000, 'std': 10000},  # 50ms
                    'raft_apply_wait': {'threshold': 30000, 'mean': 5000, 'std': 5000},  # 30ms
                    'available_size': {'threshold': 10*1024*1024*1024, 'mean': 50*1024*1024*1024, 'std': 20*1024*1024*1024},
                    # 🔧 修复：使用合理的阈值
                    'store_size': {'threshold': 100*1024*1024*1024, 'mean': 50*1024*1024*1024, 'std': 10*1024*1024*1024},  # 100GB
                    'read_mbps': {'threshold': 10*1024*1024*1024, 'mean': 5*1024*1024*1024, 'std': 1*1024*1024*1024},  # 10GB/s
                    'write_wal_mbps': {'threshold': 1*1024*1024*1024, 'mean': 500*1024*1024, 'std': 100*1024*1024},  # 1GB/s
                    'region_pending': {'threshold': 1000000, 'mean': 500000, 'std': 100000},  # 100万
                    'server_is_up': {'threshold': 0.5, 'mean': 1.0, 'std': 0.1}  # 反向逻辑
                }
            }
        }

        return {
            'component_type': component_type,
            'single_metric_thresholds': defaults.get(component_type, {}).get('single_metric_thresholds', {}),
            'correlation_thresholds': {},
            'anomaly_detection_model': None,
            'data_statistics': {},
            'is_default': True
        }

    def generate_multidim_report(self, component_type: str, anomaly_result: Dict[str, Any]) -> str:
        """生成多维度异常报告"""
        try:
            report_lines = []
            report_lines.append(f"🔍 {component_type.upper()}组件多维度异常分析")
            report_lines.append("=" * 50)

            # 综合异常评分
            score = anomaly_result.get('multidimensional_anomaly_score', 0)
            overall = anomaly_result.get('overall_anomaly', False)
            status = "🚨 异常" if overall else "✅ 正常"
            report_lines.append(f"综合异常评分: {score:.2f} {status}")

            # 单指标异常
            single_anomalies = anomaly_result.get('single_metric_anomalies', [])
            if single_anomalies:
                report_lines.append(f"\n📊 单指标异常 ({len(single_anomalies)}个):")
                for anomaly in single_anomalies:
                    metric = anomaly['metric']
                    value = anomaly['value']
                    threshold = anomaly['threshold']
                    deviation = anomaly['deviation']
                    severity = anomaly['severity']

                    # 格式化数值显示
                    if 'memory' in metric and value > 1024*1024:
                        value_str = f"{value/1024/1024/1024:.2f}GB"
                        threshold_str = f"{threshold/1024/1024/1024:.2f}GB"
                    elif 'usage' in metric or 'util' in metric:
                        value_str = f"{value:.2f}%"
                        threshold_str = f"{threshold:.2f}%"
                    elif 'duration' in metric or 'wait' in metric:
                        value_str = f"{value/1000:.1f}ms"
                        threshold_str = f"{threshold/1000:.1f}ms"
                    else:
                        value_str = f"{value:.1f}"
                        threshold_str = f"{threshold:.1f}"

                    severity_icon = {"critical": "🔴", "high": "🟠", "medium": "🟡", "low": "🟢"}.get(severity, "⚪")
                    report_lines.append(f"  {severity_icon} {metric}: {value_str} (阈值: {threshold_str}, 偏差: {deviation:.1f}x)")

            # 相关性异常
            correlation_anomalies = anomaly_result.get('correlation_anomalies', [])
            if correlation_anomalies:
                report_lines.append(f"\n🔗 相关性异常 ({len(correlation_anomalies)}个):")
                for anomaly in correlation_anomalies:
                    rule_name = anomaly['rule_name']
                    primary = ', '.join(anomaly['primary_anomalies'])
                    secondary = ', '.join(anomaly['secondary_anomalies'])
                    confidence = anomaly['confidence']
                    report_lines.append(f"  📋 {rule_name}")
                    report_lines.append(f"     主要异常: {primary}")
                    report_lines.append(f"     次要异常: {secondary}")
                    report_lines.append(f"     置信度: {confidence:.2f}")

            if not single_anomalies and not correlation_anomalies:
                report_lines.append("\n✅ 未检测到异常")

            return "\n".join(report_lines)

        except Exception as e:
            self.logger.error(f"生成多维度报告失败: {e}")
            return f"❌ 报告生成失败: {e}"
