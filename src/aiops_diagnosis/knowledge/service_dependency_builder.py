"""
服务依赖关系构建器
从Trace数据中构建完整的服务依赖图和调用链分析
"""

import pandas as pd
import networkx as nx
from typing import Dict, List, Set, Tuple, Any, Optional
from collections import defaultdict, deque
import logging
from dataclasses import dataclass
import json


@dataclass
class ServiceCall:
    """服务调用信息"""
    caller: str
    callee: str
    operation: str
    duration_ms: float
    trace_id: str
    span_id: str
    start_time: int
    has_error: bool
    error_details: Optional[str] = None


@dataclass
class ServiceNode:
    """服务节点信息"""
    name: str
    service_type: str  # frontend, backend, database, cache, middleware
    instances: Set[str]  # Pod实例
    avg_latency: float
    error_rate: float
    call_count: int


class ServiceDependencyBuilder:
    """服务依赖关系构建器"""
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
        self.dependency_graph = nx.DiGraph()
        self.service_calls: List[ServiceCall] = []
        self.service_nodes: Dict[str, ServiceNode] = {}
        
    def build_dependency_graph(self, traces_data: pd.DataFrame) -> Dict[str, Any]:
        """从Trace数据构建完整的服务依赖图"""
        self.logger.info("🔗 开始构建服务依赖关系图...")
        
        try:
            # 1. 提取所有服务调用关系
            self._extract_service_calls(traces_data)
            
            # 2. 构建服务节点信息
            self._build_service_nodes()
            
            # 3. 构建依赖图
            self._build_graph()
            
            # 4. 分析调用路径
            call_paths = self._analyze_call_paths()
            
            # 5. 识别关键路径
            critical_paths = self._identify_critical_paths()
            
            # 6. 分析服务层次
            service_layers = self._analyze_service_layers()
            
            result = {
                "dependency_graph": self._serialize_graph(),
                "service_nodes": self._serialize_service_nodes(),
                "call_paths": call_paths,
                "critical_paths": critical_paths,
                "service_layers": service_layers,
                "statistics": self._generate_statistics()
            }
            
            self.logger.info(f"✅ 服务依赖图构建完成: {len(self.service_nodes)} 个服务, {len(self.service_calls)} 个调用")
            return result
            
        except Exception as e:
            self.logger.error(f"❌ 构建服务依赖图失败: {e}")
            return {}
    
    def _extract_service_calls(self, traces_data: pd.DataFrame):
        """提取服务调用关系"""
        self.logger.info("🔍 提取服务调用关系...")
        
        # 按traceID分组处理
        for trace_id, trace_group in traces_data.groupby('traceID'):
            self._process_trace_calls(trace_id, trace_group)
    
    def _process_trace_calls(self, trace_id: str, trace_spans: pd.DataFrame):
        """处理单个trace的调用关系"""
        try:
            # 按时间排序span
            sorted_spans = trace_spans.sort_values('startTimeMillis')
            
            # 构建span层次结构
            span_hierarchy = self._build_span_hierarchy(sorted_spans)
            
            # 提取调用关系
            for _, span in sorted_spans.iterrows():
                caller, callee = self._extract_call_relationship(span, span_hierarchy)
                if caller and callee and caller != callee:
                    service_call = ServiceCall(
                        caller=caller,
                        callee=callee,
                        operation=span.get('operationName', 'unknown'),
                        duration_ms=span.get('duration', 0) / 1000,
                        trace_id=trace_id,
                        span_id=span.get('spanID', 'unknown'),
                        start_time=span.get('startTimeMillis', 0),
                        has_error=self._check_span_error(span),
                        error_details=self._extract_error_details(span)
                    )
                    self.service_calls.append(service_call)
                    
        except Exception as e:
            self.logger.debug(f"处理trace {trace_id} 失败: {e}")
    
    def _build_span_hierarchy(self, sorted_spans: pd.DataFrame) -> Dict[str, str]:
        """构建span的父子关系"""
        span_to_service = {}
        
        for _, span in sorted_spans.iterrows():
            span_id = span.get('spanID', 'unknown')
            service = self._extract_service_name(span)
            span_to_service[span_id] = service
            
        return span_to_service
    
    def _extract_call_relationship(self, span: pd.Series, span_hierarchy: Dict[str, str]) -> Tuple[str, str]:
        """从span中提取调用关系"""
        try:
            # 当前span的服务
            current_service = self._extract_service_name(span)
            
            # 检查operationName中的调用关系
            operation = span.get('operationName', '')
            if 'hipstershop.' in operation:
                # 例如: hipstershop.ProductCatalogService/GetProduct
                # 这表示调用ProductCatalogService
                target_service = self._parse_target_service_from_operation(operation)
                if target_service:
                    return current_service, target_service
            
            # 检查parentSpanID
            parent_span_id = span.get('parentSpanID', '')
            if parent_span_id and parent_span_id in span_hierarchy:
                parent_service = span_hierarchy[parent_span_id]
                if parent_service != current_service:
                    return parent_service, current_service
            
            return None, None
            
        except Exception as e:
            self.logger.debug(f"提取调用关系失败: {e}")
            return None, None
    
    def _parse_target_service_from_operation(self, operation: str) -> Optional[str]:
        """从operationName解析目标服务"""
        service_mapping = {
            'CartService': 'cartservice',
            'CheckoutService': 'checkoutservice', 
            'PaymentService': 'paymentservice',
            'ProductCatalogService': 'productcatalogservice',
            'RecommendationService': 'recommendationservice',
            'AdService': 'adservice',
            'CurrencyService': 'currencyservice',
            'ShippingService': 'shippingservice',
            'EmailService': 'emailservice'
        }
        
        for service_class, service_name in service_mapping.items():
            if service_class in operation:
                return service_name
        
        return None
    
    def _extract_service_name(self, span: pd.Series) -> str:
        """提取服务名称"""
        try:
            # 从process.serviceName提取
            process = span.get('process', {})
            if isinstance(process, dict):
                service_name = process.get('serviceName', '')
                if service_name and service_name != 'unknown':
                    return service_name.lower()
            
            # 从tags中提取
            tags = span.get('tags', [])
            if isinstance(tags, list):
                for tag in tags:
                    if isinstance(tag, dict) and tag.get('key') == 'service.name':
                        return tag.get('value', 'unknown').lower()
            
            # 从operationName推断
            operation = span.get('operationName', '')
            if operation.startswith('/'):
                return 'frontend'  # HTTP请求通常来自frontend
            
            return 'unknown'
            
        except Exception as e:
            self.logger.debug(f"提取服务名失败: {e}")
            return 'unknown'
    
    def _check_span_error(self, span: pd.Series) -> bool:
        """检查span是否有错误"""
        try:
            # 检查tags中的error标记
            tags = span.get('tags', [])
            if isinstance(tags, list):
                for tag in tags:
                    if isinstance(tag, dict):
                        if tag.get('key') == 'error' and tag.get('value') is True:
                            return True
                        if tag.get('key') == 'http.status_code':
                            status_code = tag.get('value', 200)
                            if isinstance(status_code, (int, str)) and int(status_code) >= 400:
                                return True
            
            # 检查logs中的错误信息
            logs = span.get('logs', [])
            if isinstance(logs, list):
                for log in logs:
                    if isinstance(log, dict):
                        fields = log.get('fields', [])
                        for field in fields:
                            if isinstance(field, dict) and 'error' in str(field.get('value', '')).lower():
                                return True
            
            return False
            
        except Exception as e:
            self.logger.debug(f"检查span错误失败: {e}")
            return False
    
    def _extract_error_details(self, span: pd.Series) -> Optional[str]:
        """提取错误详情"""
        if not self._check_span_error(span):
            return None
        
        try:
            error_details = []
            
            # 从logs中提取错误信息
            logs = span.get('logs', [])
            if isinstance(logs, list):
                for log in logs:
                    if isinstance(log, dict):
                        fields = log.get('fields', [])
                        for field in fields:
                            if isinstance(field, dict) and field.get('key') == 'error.object':
                                error_details.append(str(field.get('value', '')))
            
            return '; '.join(error_details) if error_details else 'Unknown error'
            
        except Exception as e:
            self.logger.debug(f"提取错误详情失败: {e}")
            return 'Error extraction failed'

    def _build_service_nodes(self):
        """构建服务节点信息"""
        self.logger.info("🏗️ 构建服务节点信息...")

        service_stats = defaultdict(lambda: {
            'total_duration': 0,
            'call_count': 0,
            'error_count': 0,
            'instances': set()
        })

        # 统计每个服务的信息
        for call in self.service_calls:
            # 统计被调用方
            stats = service_stats[call.callee]
            stats['total_duration'] += call.duration_ms
            stats['call_count'] += 1
            if call.has_error:
                stats['error_count'] += 1

        # 创建服务节点
        for service_name, stats in service_stats.items():
            if stats['call_count'] > 0:
                avg_latency = stats['total_duration'] / stats['call_count']
                error_rate = stats['error_count'] / stats['call_count']

                self.service_nodes[service_name] = ServiceNode(
                    name=service_name,
                    service_type=self._classify_service_type(service_name),
                    instances=stats['instances'],
                    avg_latency=avg_latency,
                    error_rate=error_rate,
                    call_count=stats['call_count']
                )

    def _classify_service_type(self, service_name: str) -> str:
        """分类服务类型"""
        if service_name in ['frontend', 'gateway', 'proxy']:
            return 'frontend'
        elif service_name in ['redis', 'memcached']:
            return 'cache'
        elif service_name in ['tidb', 'mysql', 'postgres', 'database']:
            return 'database'
        elif 'service' in service_name:
            return 'backend'
        else:
            return 'middleware'

    def _build_graph(self):
        """构建依赖图"""
        self.logger.info("📊 构建NetworkX依赖图...")

        # 添加节点
        for service_name, node in self.service_nodes.items():
            self.dependency_graph.add_node(
                service_name,
                service_type=node.service_type,
                avg_latency=node.avg_latency,
                error_rate=node.error_rate,
                call_count=node.call_count
            )

        # 添加边（调用关系）
        edge_stats = defaultdict(lambda: {
            'call_count': 0,
            'total_duration': 0,
            'error_count': 0
        })

        for call in self.service_calls:
            edge_key = (call.caller, call.callee)
            stats = edge_stats[edge_key]
            stats['call_count'] += 1
            stats['total_duration'] += call.duration_ms
            if call.has_error:
                stats['error_count'] += 1

        # 添加边到图中
        for (caller, callee), stats in edge_stats.items():
            if stats['call_count'] > 0:
                avg_duration = stats['total_duration'] / stats['call_count']
                error_rate = stats['error_count'] / stats['call_count']

                self.dependency_graph.add_edge(
                    caller, callee,
                    call_count=stats['call_count'],
                    avg_duration=avg_duration,
                    error_rate=error_rate
                )

    def _analyze_call_paths(self) -> List[Dict[str, Any]]:
        """分析调用路径"""
        self.logger.info("🛤️ 分析服务调用路径...")

        call_paths = []

        # 找到所有入口服务（没有入边的服务）
        entry_services = [node for node in self.dependency_graph.nodes()
                         if self.dependency_graph.in_degree(node) == 0]

        # 从每个入口服务开始，找到所有可能的路径
        for entry in entry_services:
            paths = self._find_all_paths_from_service(entry)
            call_paths.extend(paths)

        return call_paths

    def _find_all_paths_from_service(self, start_service: str, max_depth: int = 10) -> List[Dict[str, Any]]:
        """从指定服务开始找到所有调用路径"""
        paths = []

        def dfs(current_service: str, path: List[str], total_latency: float, depth: int):
            if depth > max_depth:
                return

            # 记录当前路径
            if len(path) > 1:  # 至少有一个调用关系
                paths.append({
                    'path': path.copy(),
                    'total_latency': total_latency,
                    'depth': depth,
                    'entry_service': start_service,
                    'leaf_service': current_service
                })

            # 继续探索下游服务
            for successor in self.dependency_graph.successors(current_service):
                edge_data = self.dependency_graph[current_service][successor]
                new_latency = total_latency + edge_data.get('avg_duration', 0)
                dfs(successor, path + [successor], new_latency, depth + 1)

        dfs(start_service, [start_service], 0, 0)
        return paths

    def _identify_critical_paths(self) -> List[Dict[str, Any]]:
        """识别关键路径（高延迟、高错误率）"""
        self.logger.info("🎯 识别关键调用路径...")

        all_paths = self._analyze_call_paths()

        # 按延迟排序，找到最慢的路径
        critical_paths = []

        # 高延迟路径
        latency_sorted = sorted(all_paths, key=lambda x: x['total_latency'], reverse=True)
        for path in latency_sorted[:5]:  # 取前5个最慢的路径
            critical_paths.append({
                'type': 'high_latency',
                'path': path['path'],
                'total_latency': path['total_latency'],
                'criticality_score': path['total_latency'] / 1000  # 转换为秒作为评分
            })

        # 高错误率路径
        error_paths = []
        for path in all_paths:
            path_error_rate = self._calculate_path_error_rate(path['path'])
            if path_error_rate > 0.05:  # 错误率超过5%
                error_paths.append({
                    'type': 'high_error_rate',
                    'path': path['path'],
                    'error_rate': path_error_rate,
                    'criticality_score': path_error_rate * 10  # 错误率*10作为评分
                })

        critical_paths.extend(error_paths)

        # 按关键性评分排序
        critical_paths.sort(key=lambda x: x['criticality_score'], reverse=True)

        return critical_paths[:10]  # 返回前10个关键路径

    def _calculate_path_error_rate(self, path: List[str]) -> float:
        """计算路径的错误率"""
        total_errors = 0
        total_calls = 0

        for i in range(len(path) - 1):
            caller, callee = path[i], path[i + 1]
            if self.dependency_graph.has_edge(caller, callee):
                edge_data = self.dependency_graph[caller][callee]
                total_errors += edge_data.get('error_count', 0)
                total_calls += edge_data.get('call_count', 0)

        return total_errors / total_calls if total_calls > 0 else 0

    def _analyze_service_layers(self) -> Dict[str, List[str]]:
        """分析服务层次结构"""
        self.logger.info("🏗️ 分析服务层次结构...")

        layers = {
            'frontend': [],
            'backend': [],
            'database': [],
            'cache': [],
            'middleware': []
        }

        for service_name, node in self.service_nodes.items():
            layers[node.service_type].append(service_name)

        return layers

    def _serialize_graph(self) -> Dict[str, Any]:
        """序列化依赖图为JSON格式"""
        return {
            'nodes': [
                {
                    'id': node,
                    'service_type': data.get('service_type', 'unknown'),
                    'avg_latency': data.get('avg_latency', 0),
                    'error_rate': data.get('error_rate', 0),
                    'call_count': data.get('call_count', 0)
                }
                for node, data in self.dependency_graph.nodes(data=True)
            ],
            'edges': [
                {
                    'source': source,
                    'target': target,
                    'call_count': data.get('call_count', 0),
                    'avg_duration': data.get('avg_duration', 0),
                    'error_rate': data.get('error_rate', 0)
                }
                for source, target, data in self.dependency_graph.edges(data=True)
            ]
        }

    def _serialize_service_nodes(self) -> Dict[str, Dict[str, Any]]:
        """序列化服务节点信息"""
        return {
            name: {
                'service_type': node.service_type,
                'instances': list(node.instances),
                'avg_latency': node.avg_latency,
                'error_rate': node.error_rate,
                'call_count': node.call_count
            }
            for name, node in self.service_nodes.items()
        }

    def _generate_statistics(self) -> Dict[str, Any]:
        """生成统计信息"""
        return {
            'total_services': len(self.service_nodes),
            'total_calls': len(self.service_calls),
            'service_types': {
                service_type: len([n for n in self.service_nodes.values() if n.service_type == service_type])
                for service_type in ['frontend', 'backend', 'database', 'cache', 'middleware']
            },
            'avg_latency_by_type': self._calculate_avg_latency_by_type(),
            'error_rate_by_type': self._calculate_error_rate_by_type()
        }

    def _calculate_avg_latency_by_type(self) -> Dict[str, float]:
        """按服务类型计算平均延迟"""
        type_stats = defaultdict(list)

        for node in self.service_nodes.values():
            type_stats[node.service_type].append(node.avg_latency)

        return {
            service_type: sum(latencies) / len(latencies) if latencies else 0
            for service_type, latencies in type_stats.items()
        }

    def _calculate_error_rate_by_type(self) -> Dict[str, float]:
        """按服务类型计算错误率"""
        type_stats = defaultdict(list)

        for node in self.service_nodes.values():
            type_stats[node.service_type].append(node.error_rate)

        return {
            service_type: sum(error_rates) / len(error_rates) if error_rates else 0
            for service_type, error_rates in type_stats.items()
        }

    def find_dependency_path(self, source: str, target: str) -> Optional[List[str]]:
        """查找两个服务之间的依赖路径"""
        try:
            if source in self.dependency_graph and target in self.dependency_graph:
                path = nx.shortest_path(self.dependency_graph, source, target)
                return path
        except nx.NetworkXNoPath:
            pass

        return None

    def get_service_dependencies(self, service_name: str) -> Dict[str, List[str]]:
        """获取指定服务的依赖关系"""
        dependencies = {
            'upstream': [],  # 调用当前服务的服务
            'downstream': []  # 当前服务调用的服务
        }

        if service_name in self.dependency_graph:
            dependencies['upstream'] = list(self.dependency_graph.predecessors(service_name))
            dependencies['downstream'] = list(self.dependency_graph.successors(service_name))

        return dependencies

    def analyze_service_impact(self, service_name: str) -> Dict[str, Any]:
        """分析服务影响范围"""
        if service_name not in self.dependency_graph:
            return {'impact_scope': 'unknown', 'affected_services': []}

        # 找到所有受影响的下游服务
        affected_services = set()

        def dfs_downstream(current_service: str, visited: Set[str]):
            if current_service in visited:
                return
            visited.add(current_service)
            affected_services.add(current_service)

            for successor in self.dependency_graph.successors(current_service):
                dfs_downstream(successor, visited)

        dfs_downstream(service_name, set())
        affected_services.discard(service_name)  # 移除自身

        return {
            'impact_scope': 'high' if len(affected_services) > 5 else 'medium' if len(affected_services) > 2 else 'low',
            'affected_services': list(affected_services),
            'direct_dependencies': list(self.dependency_graph.successors(service_name)),
            'dependency_count': len(affected_services)
        }
