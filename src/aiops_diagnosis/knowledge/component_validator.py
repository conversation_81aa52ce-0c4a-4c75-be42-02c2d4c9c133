#!/usr/bin/env python3
"""
组件候选框约束机制
确保推理结果只能从有效的组件白名单中选择
"""

from typing import Dict, List, Set, Optional, Tuple, Any
import re
import logging
import os
from pathlib import Path
from dataclasses import dataclass

from .hipstershop_architecture import HipsterShopArchitecture, ComponentType


@dataclass
class ComponentCandidate:
    """组件候选项"""
    name: str
    component_type: ComponentType
    confidence: float
    evidence_count: int
    description: str


class ComponentValidator:
    """组件验证器"""
    
    def __init__(self):
        self.architecture = HipsterShopArchitecture()
        self.logger = logging.getLogger(__name__)
        
        # 构建组件白名单
        self._build_component_whitelist()
        
        # 构建组件别名映射
        self._build_alias_mapping()
    
    def _build_component_whitelist(self):
        """构建组件白名单 - 从实际数据目录动态获取真实组件名称"""

        # 基础组件集合
        self.valid_components = self.architecture.get_all_valid_components()

        # 从实际数据目录中扫描真实的组件名称
        real_components = self._scan_real_components_from_data()

        # 合并基础组件和真实组件
        self.valid_components.update(real_components)

        # 添加常见的组件变体
        additional_components = set()

        # 添加不带后缀的服务名
        for component in self.valid_components:
            if component.endswith('service'):
                base_name = component[:-7]  # 去掉'service'
                additional_components.add(base_name)

        # 添加大写变体（仅对Service级别）
        for component in list(self.valid_components):
            if not ('-' in component and component.split('-')[-1].isdigit()):  # 不是Pod名称
                additional_components.add(component.upper())
                additional_components.add(component.capitalize())

        self.valid_components.update(additional_components)

        self.logger.info(f"构建多层级组件白名单: {len(self.valid_components)}个有效组件")
        self.logger.info(f"包含 {len(real_components)} 个从数据目录扫描的真实组件")

    def _scan_real_components_from_data(self) -> set:
        """从实际数据目录扫描真实的组件名称"""

        real_components = set()

        # 使用数据路径管理器获取配置化的路径
        from ..config.data_path_manager import get_data_path_manager

        # 尝试多个可能的数据路径
        path_manager = get_data_path_manager()
        possible_data_paths = [
            path_manager.get_data_root()  # 主数据路径
        ] + path_manager.get_config().fallback_paths  # 备用路径

        data_root = None
        for path in possible_data_paths:
            if os.path.exists(path):
                data_root = Path(path)
                break

        if not data_root:
            self.logger.warning("未找到数据目录，使用默认组件列表")
            return self._get_default_components()

        self.logger.info(f"扫描数据目录: {data_root}")

        try:
            # 扫描APM Pod文件获取真实Pod名称
            real_components.update(self._scan_apm_pod_components(data_root))

            # 扫描APM Service文件获取真实Service名称
            real_components.update(self._scan_apm_service_components(data_root))

            # 扫描基础设施组件
            real_components.update(self._scan_infra_components(data_root))

            # 扫描Node名称（从日志或其他数据中）
            real_components.update(self._scan_node_components(data_root))

        except Exception as e:
            self.logger.warning(f"扫描数据目录失败: {e}")
            return self._get_default_components()

        return real_components

    def _scan_apm_pod_components(self, data_root: Path) -> set:
        """扫描APM Pod文件获取真实Pod名称"""

        pod_components = set()

        # 查找APM Pod目录: /data/data/2025-06-*/metric-parquet/apm/pod/
        for date_dir in data_root.glob("*/metric-parquet/apm/pod"):
            if date_dir.is_dir():
                # 扫描pod文件: pod_adservice-0_2025-06-09.parquet
                for pod_file in date_dir.glob("pod_*_*.parquet"):
                    filename = pod_file.name
                    # 提取Pod名称: pod_adservice-0_2025-06-09.parquet -> adservice-0
                    if filename.startswith("pod_") and "_" in filename:
                        parts = filename.split("_")
                        if len(parts) >= 3:
                            pod_name = parts[1]  # adservice-0
                            # 过滤掉包含"(deleted)"的Pod
                            if "(deleted)" not in pod_name:
                                pod_components.add(pod_name)

                                # 同时添加Service名称
                                if "-" in pod_name:
                                    service_name = pod_name.rsplit("-", 1)[0]  # adservice
                                    pod_components.add(service_name)

        self.logger.info(f"从APM Pod文件扫描到 {len(pod_components)} 个组件")
        return pod_components

    def _scan_apm_service_components(self, data_root: Path) -> set:
        """扫描APM Service文件获取真实Service名称"""

        service_components = set()

        # 查找APM Service目录: /data/data/2025-06-*/metric-parquet/apm/service/
        for date_dir in data_root.glob("*/metric-parquet/apm/service"):
            if date_dir.is_dir():
                # 扫描service文件: service_adservice_2025-06-09.parquet
                for service_file in date_dir.glob("service_*_*.parquet"):
                    filename = service_file.name
                    # 提取Service名称: service_adservice_2025-06-09.parquet -> adservice
                    if filename.startswith("service_") and "_" in filename:
                        parts = filename.split("_")
                        if len(parts) >= 3:
                            service_name = parts[1]  # adservice
                            service_components.add(service_name)

        self.logger.info(f"从APM Service文件扫描到 {len(service_components)} 个组件")
        return service_components

    def _scan_infra_components(self, data_root: Path) -> set:
        """扫描基础设施组件"""

        infra_components = set()

        # 扫描infra目录下的组件: /data/data/2025-06-*/metric-parquet/infra/
        for date_dir in data_root.glob("*/metric-parquet/infra"):
            if date_dir.is_dir():
                # 扫描infra子目录: infra_tidb, infra_node, infra_pod等
                for infra_subdir in date_dir.iterdir():
                    if infra_subdir.is_dir():
                        subdir_name = infra_subdir.name
                        if subdir_name.startswith("infra_"):
                            component_name = subdir_name[6:]  # 去掉infra_前缀
                            infra_components.add(component_name)

        # 扫描other目录: /data/data/2025-06-*/metric-parquet/other/
        for date_dir in data_root.glob("*/metric-parquet/other"):
            if date_dir.is_dir():
                for other_file in date_dir.glob("infra_*_*.parquet"):
                    filename = other_file.name
                    # infra_tikv_available_size_2025-05-27.parquet -> tikv
                    if filename.startswith("infra_") and "_" in filename:
                        parts = filename.split("_")
                        if len(parts) >= 3:
                            component_name = parts[1]  # tikv, pd等
                            infra_components.add(component_name)

        self.logger.info(f"从基础设施文件扫描到 {len(infra_components)} 个组件")
        return infra_components

    def _scan_node_components(self, data_root: Path) -> set:
        """扫描Node组件名称"""

        node_components = set()

        # 从infra_node文件中扫描Node名称: /data/data/2025-06-*/metric-parquet/infra/infra_node/
        for date_dir in data_root.glob("*/metric-parquet/infra/infra_node"):
            if date_dir.is_dir():
                for node_file in date_dir.glob("*.parquet"):
                    # 尝试从文件内容中读取Node名称
                    try:
                        import pandas as pd
                        df = pd.read_parquet(node_file)

                        # 检查多个可能的Node字段名
                        node_column = None
                        for col in ['kubernetes_node', 'node', 'instance']:
                            if col in df.columns:
                                node_column = col
                                break

                        if node_column:
                            unique_nodes = df[node_column].unique()
                            for node in unique_nodes:
                                if isinstance(node, str):
                                    # 检查是否是有效的Node名称格式
                                    if (node.startswith('aiops-k8s-') or
                                        node.startswith('k8s-master') or
                                        node.startswith('k8s-worker') or
                                        node.startswith('k8s-node')):
                                        node_components.add(node)

                        # 找到Node数据后就退出，避免重复扫描
                        if node_components:
                            break
                    except Exception as e:
                        # 如果读取失败，继续尝试下一个文件
                        self.logger.debug(f"读取Node文件失败 {node_file}: {e}")
                        continue

                # 如果已经找到Node数据，就不需要继续扫描其他日期
                if node_components:
                    break

        # 如果没有扫描到Node，记录警告但不生成硬编码的Node名称
        if not node_components:
            self.logger.warning("⚠️ 未扫描到任何Node组件，请检查数据目录中是否包含Node相关数据")

        self.logger.info(f"扫描到 {len(node_components)} 个Node组件")
        return node_components

    def _get_default_components(self) -> set:
        """获取默认的组件列表（当无法扫描数据目录时使用）- 移除硬编码，返回空集合"""

        self.logger.warning("⚠️ 无法扫描数据目录，返回空的组件列表")
        self.logger.warning("⚠️ 系统将依赖动态扫描，请检查数据目录路径是否正确")

        # 🔧 移除硬编码，返回空集合，强制依赖动态扫描
        return set()

    def _build_alias_mapping(self):
        """构建组件别名映射 - 基于动态扫描结果生成别名"""

        self.alias_mapping = {}

        # 🔧 基于实际扫描到的组件动态生成别名
        for component in self.valid_components:
            # 为每个组件生成常见的别名模式
            self._generate_component_aliases(component)

        self.logger.info(f"动态生成 {len(self.alias_mapping)} 个组件别名映射")

    def _generate_component_aliases(self, component: str):
        """为单个组件生成别名"""

        # 基本别名：组件名本身
        self.alias_mapping[component] = component
        self.alias_mapping[component.upper()] = component
        self.alias_mapping[component.capitalize()] = component

        # 如果是service结尾的组件，生成去掉service的别名
        if component.endswith('service'):
            base_name = component[:-7]  # 去掉'service'
            self.alias_mapping[base_name] = component
            self.alias_mapping[base_name.upper()] = component
            self.alias_mapping[base_name.capitalize()] = component

            # 生成常见的功能别名
            self._generate_functional_aliases(component, base_name)

        # 如果是Pod名称（包含-数字），生成服务名别名
        if '-' in component and component.split('-')[-1].isdigit():
            service_name = component.rsplit('-', 1)[0]
            self.alias_mapping[service_name] = component

    def _generate_functional_aliases(self, full_name: str, base_name: str):
        """基于组件功能生成别名（避免硬编码）"""

        # 基于组件名称的语义生成别名
        functional_mappings = {
            'payment': ['pay', 'billing'],
            'checkout': ['order', 'purchase'],
            'cart': ['basket', 'shopping'],
            'product': ['catalog', 'inventory'],
            'recommendation': ['recommend', 'suggest'],
            'ad': ['advertisement', 'ads'],
            'currency': ['exchange', 'forex'],
            'shipping': ['delivery', 'logistics'],
            'email': ['mail', 'notification'],
            'frontend': ['front', 'web', 'ui']
        }

        # 只为实际存在的组件生成功能别名
        for key, aliases in functional_mappings.items():
            if key in base_name.lower():
                for alias in aliases:
                    self.alias_mapping[alias] = full_name
                    self.alias_mapping[alias.upper()] = full_name
                    self.alias_mapping[alias.capitalize()] = full_name
    
    def normalize_component_name(self, component_name: str) -> Optional[str]:
        """标准化组件名称 - 严格基于组件列表验证，不使用关键字匹配"""

        if not component_name or not isinstance(component_name, str):
            return None

        component_name = component_name.strip()

        # 1. 直接精确匹配（最高优先级）
        if component_name in self.valid_components:
            return component_name

        # 2. 大小写不敏感的精确匹配
        component_lower = component_name.lower()
        for valid_component in self.valid_components:
            if valid_component.lower() == component_lower:
                return valid_component

        # 3. 别名映射（仅限预定义的别名）
        if component_name in self.alias_mapping:
            return self.alias_mapping[component_name]

        # 4. 严格的前缀匹配（仅限于实际存在的组件）
        # 例如：frontend-0 匹配到实际存在的 frontend-0，而不是通过模式推断
        for valid_component in self.valid_components:
            # 检查是否为Pod实例的基础服务名匹配
            if '-' in valid_component and component_name == valid_component.split('-')[0]:
                # 但只有当这个基础服务名确实存在时才返回
                base_service = valid_component.split('-')[0]
                if base_service in self.valid_components:
                    return base_service

        # 5. 不再使用模糊匹配和模式匹配，严格按列表验证
        return None

    def _is_valid_pod_name(self, component_name: str) -> bool:
        """检查是否为有效的Pod名称 - 基于动态扫描结果验证"""

        # 🔧 移除硬编码，直接检查是否在扫描到的有效组件中
        if component_name in self.valid_components:
            return True

        # 检查是否符合Pod名称的通用模式（服务名-数字）
        if '-' in component_name:
            parts = component_name.rsplit('-', 1)
            if len(parts) == 2 and parts[1].isdigit():
                # 检查服务名部分是否在有效组件中
                service_name = parts[0]
                return service_name in self.valid_components

        return False

    def _is_valid_node_name(self, component_name: str) -> bool:
        """检查是否为有效的Node名称 - 基于动态扫描结果验证"""

        # 🔧 移除硬编码，直接检查是否在扫描到的有效组件中
        return component_name in self.valid_components

    def _fuzzy_match(self, component_name: str) -> Optional[str]:
        """模糊匹配组件名称"""
        
        component_lower = component_name.lower()
        
        # 检查是否包含已知服务名
        for valid_component in self.valid_components:
            valid_lower = valid_component.lower()
            
            # 包含匹配
            if valid_lower in component_lower or component_lower in valid_lower:
                return valid_component
            
            # 编辑距离匹配（简单版本）
            if self._simple_edit_distance(component_lower, valid_lower) <= 2:
                return valid_component
        
        return None
    
    def _simple_edit_distance(self, s1: str, s2: str) -> int:
        """简单编辑距离计算"""
        if len(s1) > len(s2):
            s1, s2 = s2, s1
        
        distances = range(len(s1) + 1)
        for i2, c2 in enumerate(s2):
            distances_ = [i2 + 1]
            for i1, c1 in enumerate(s1):
                if c1 == c2:
                    distances_.append(distances[i1])
                else:
                    distances_.append(1 + min((distances[i1], distances[i1 + 1], distances_[-1])))
            distances = distances_
        return distances[-1]
    
    def _extract_from_compound_name(self, component_name: str) -> Optional[str]:
        """从复合名称中提取组件"""
        
        # 处理类似 "unknown (43 errors)/checkoutservice (10 errors)" 的情况
        if '/' in component_name:
            parts = component_name.split('/')
            for part in parts:
                # 提取括号前的部分
                clean_part = re.sub(r'\s*\([^)]*\)', '', part).strip()
                normalized = self.normalize_component_name(clean_part)
                if normalized:
                    return normalized
        
        # 处理括号内的信息
        match = re.search(r'([^()]+)', component_name)
        if match:
            clean_name = match.group(1).strip()
            if clean_name != component_name:  # 避免无限递归
                return self.normalize_component_name(clean_name)
        
        return None
    
    def validate_and_suggest_components(self, candidate_components: List[str]) -> List[ComponentCandidate]:
        """验证并建议组件"""
        
        validated_candidates = []
        
        for candidate in candidate_components:
            normalized = self.normalize_component_name(candidate)
            
            if normalized:
                component_type = self.architecture.get_component_type(normalized)
                confidence = self._calculate_validation_confidence(candidate, normalized)
                
                # 获取组件描述
                description = self._get_component_description(normalized)
                
                validated_candidate = ComponentCandidate(
                    name=normalized,
                    component_type=component_type or ComponentType.SERVICE,
                    confidence=confidence,
                    evidence_count=1,  # 这里可以根据实际证据数量调整
                    description=description
                )
                
                validated_candidates.append(validated_candidate)
            else:
                self.logger.warning(f"无法验证组件: {candidate}")
        
        # 去重并排序
        unique_candidates = {}
        for candidate in validated_candidates:
            if candidate.name not in unique_candidates:
                unique_candidates[candidate.name] = candidate
            else:
                # 合并证据数量
                existing = unique_candidates[candidate.name]
                existing.evidence_count += candidate.evidence_count
                existing.confidence = max(existing.confidence, candidate.confidence)
        
        # 按置信度排序
        sorted_candidates = sorted(unique_candidates.values(), 
                                 key=lambda x: x.confidence, reverse=True)
        
        return sorted_candidates
    
    def _calculate_validation_confidence(self, original: str, normalized: str) -> float:
        """计算验证置信度"""
        
        if original == normalized:
            return 1.0  # 完全匹配
        
        if original.lower() == normalized.lower():
            return 0.95  # 大小写不同
        
        if original in self.alias_mapping and self.alias_mapping[original] == normalized:
            return 0.9  # 别名匹配
        
        if normalized.lower() in original.lower() or original.lower() in normalized.lower():
            return 0.8  # 包含匹配
        
        return 0.7  # 模糊匹配
    
    def _get_component_description(self, component: str) -> str:
        """获取组件描述 - 支持多层级精确描述"""

        # Service级别描述
        if component in self.architecture.all_components:
            return self.architecture.all_components[component].description

        # Pod级别描述
        if self._is_valid_pod_name(component):
            # 提取服务名称
            service_name = component.split('-')[0]
            if service_name in self.architecture.all_components:
                return f"{self.architecture.all_components[service_name].description} (Pod实例: {component})"
            else:
                return f"Pod实例: {component}"

        # Node级别描述
        if self._is_valid_node_name(component):
            return f"Kubernetes工作节点: {component}"

        # 传统节点检查（兼容性）
        if component in self.architecture.nodes:
            return f"Kubernetes节点: {component}"

        return f"组件: {component}"
    
   
     
    def _extract_component_from_reasoning(self, reasoning_result: Dict[str, Any]) -> Optional[str]:
        """从推理结果中提取组件"""
        
        # 从推理轨迹中提取
        reasoning_trace = reasoning_result.get('reasoning_trace', [])
        for step in reasoning_trace:
            observation = step.get('observation', '')
            
            # 查找提到的组件名称
            for component in self.valid_components:
                if component.lower() in observation.lower():
                    return component
        
        # 从原因描述中提取
        reason = reasoning_result.get('reason', '')
        for component in self.valid_components:
            if component.lower() in reason.lower():
                return component
        
        return None
    
    def _get_validation_method(self, original: str, normalized: str) -> str:
        """获取验证方法"""
        
        if original == normalized:
            return 'exact_match'
        elif original.lower() == normalized.lower():
            return 'case_insensitive_match'
        elif original in self.alias_mapping:
            return 'alias_mapping'
        elif normalized.lower() in original.lower():
            return 'substring_match'
        else:
            return 'fuzzy_match'
    
    def _select_fallback_component(self, reasoning_result: Dict[str, Any]) -> str:
        """智能选择备用组件 - 基于推理过程而非硬编码"""

        # 首先尝试从推理轨迹中提取真正的组件
        extracted_component = self._extract_component_from_reasoning_trace(reasoning_result)
        if extracted_component and self.normalize_component_name(extracted_component):
            return extracted_component

        # 从多Agent验证结果中提取组件
        multi_agent_component = self._extract_component_from_multi_agent_results(reasoning_result)
        if multi_agent_component and self.normalize_component_name(multi_agent_component):
            return multi_agent_component

        # 从影响分析中提取主要受影响组件
        impact_component = self._extract_component_from_impact_analysis(reasoning_result)
        if impact_component and self.normalize_component_name(impact_component):
            return impact_component

        # 最后的回退：基于系统关键程度
        critical_components = ['frontend', 'checkoutservice', 'paymentservice', 'cartservice']

        # 从推理轨迹中查找线索
        reasoning_trace = reasoning_result.get('reasoning_trace', [])
        for component in critical_components:
            for step in reasoning_trace:
                observation = step.get('observation', '').lower()
                if component in observation:
                    return component

        # 最终默认（应该很少到达这里）
        return 'frontend'

    def _extract_component_from_reasoning_trace(self, reasoning_result: Dict[str, Any]) -> Optional[str]:
        """从推理轨迹中提取组件"""

        reasoning_trace = reasoning_result.get('reasoning_trace', [])

        # 查找CandidateAnalysis和EvidenceAssessment步骤中的组件
        for step in reasoning_trace:
            action = step.get('action', '')

            # 从CandidateAnalysis中提取
            if 'CandidateAnalysis' in action:
                observation = step.get('observation', '')
                # 解析 "productcatalogservice leads with X.XX confidence"
                if 'leads with' in observation:
                    component = observation.split(' leads with')[0].strip()
                    if component and component != 'unknown':
                        return component

            # 从EvidenceAssessment中提取
            elif 'EvidenceAssessment' in action:
                # 解析 "EvidenceAssessment(productcatalogservice)"
                if '(' in action and ')' in action:
                    component = action.split('(')[1].split(')')[0].strip()
                    if component and component != 'unknown':
                        return component

        return None

    def _extract_component_from_multi_agent_results(self, reasoning_result: Dict[str, Any]) -> Optional[str]:
        """从多Agent验证结果中提取组件"""

        # 检查多Agent分析结果
        multi_agent_analysis = reasoning_result.get('multi_round_analysis', {})

        # 从假设生成中提取
        hypothesis = multi_agent_analysis.get('hypothesis_generation', {})
        if isinstance(hypothesis, dict) and 'component' in hypothesis:
            component = hypothesis['component']
            if component and component != 'unknown':
                return component

        # 从验证结果中提取
        verification_results = multi_agent_analysis.get('verification_results', {})
        if isinstance(verification_results, dict):
            affected_component = verification_results.get('affected_component')
            if affected_component and affected_component != 'unknown':
                return affected_component

        return None

    def _extract_component_from_impact_analysis(self, reasoning_result: Dict[str, Any]) -> Optional[str]:
        """从影响分析中提取主要受影响组件"""

        impact_analysis = reasoning_result.get('impact_analysis', {})

        # 检查受影响组件列表
        affected_components = impact_analysis.get('affected_components', [])
        if affected_components and isinstance(affected_components, list):
            # 返回第一个有效的受影响组件
            for component in affected_components:
                if component and component != 'unknown' and self.normalize_component_name(component):
                    return component

        return None
    
    def get_component_whitelist(self) -> Set[str]:
        """获取组件白名单"""
        return self.valid_components.copy()
    
    def is_valid_component(self, component: str) -> bool:
        """检查组件是否有效"""
        return self.normalize_component_name(component) is not None
