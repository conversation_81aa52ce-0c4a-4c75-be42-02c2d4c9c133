# AIOps数据路径配置文件
# 集中管理所有数据存储路径，避免硬编码

# 主数据根目录
data_root: "/data/data"

# 备用数据路径（按优先级排序）
fallback_paths:
  - "/data/data"
  - "/opt/aiops/data"
  - "/home/<USER>/aiops"
  - "./data"

# 数据目录结构配置
directory_structure:
  # 日志数据
  logs:
    base_dir: "log-parquet"
    pattern: "log_filebeat-server_{date}_{hour}-00-00.parquet"
    time_granularity: "hour"
    
  # 调用链数据  
  traces:
    base_dir: "trace-parquet"
    pattern: "trace_jaeger-span_{date}_{hour}-00-00.parquet"
    time_granularity: "hour"
    
  # 指标数据
  metrics:
    # APM指标
    apm:
      pod:
        base_dir: "metric-parquet/apm/pod"
        pattern: "pod_{service}-{instance}_{date}.parquet"
        time_granularity: "day"
      service:
        base_dir: "metric-parquet/apm/service"
        pattern: "service_{service}_{date}.parquet"
        time_granularity: "day"
    
    # 基础设施指标
    infrastructure:
      node:
        base_dir: "metric-parquet/infra/infra_node"
        pattern: "infra_node_{metric_type}_{date}.parquet"
        time_granularity: "day"
      pod:
        base_dir: "metric-parquet/infra/infra_pod"
        pattern: "infra_pod_{metric_type}_{date}.parquet"
        time_granularity: "day"
      general:
        base_dir: "metric-parquet/infra"
        pattern: "*{metric_type}_{date}.parquet"
        time_granularity: "day"
    
    # 其他组件指标
    other:
      base_dir: "metric-parquet/other"
      pattern: "infra_{component}_{metric_name}_{date}.parquet"
      time_granularity: "day"

# 环境变量覆盖配置
environment_overrides:
  # 主数据路径环境变量
  data_root_env: "AIOPS_DATA_ROOT"
  
  # 其他路径环境变量
  logs_env: "AIOPS_LOGS_PATH"
  traces_env: "AIOPS_TRACES_PATH"
  metrics_env: "AIOPS_METRICS_PATH"

# 路径验证配置
validation:
  # 是否检查路径存在性
  check_existence: true
  
  # 是否创建不存在的目录
  create_missing: false
  
  # 必需的子目录
  required_subdirs:
    - "log-parquet"
    - "trace-parquet"
    - "metric-parquet/apm/pod"
    - "metric-parquet/apm/service"
    - "metric-parquet/infra"
    - "metric-parquet/other"

# 缓存配置
cache:
  # 路径缓存时间（秒）
  path_cache_ttl: 300
  
  # 是否启用路径缓存
  enable_caching: true

# 性能优化配置
performance:
  # 并发文件扫描
  concurrent_scan: true
  max_scan_workers: 4
  
  # 文件扫描超时（秒）
  scan_timeout: 30
  
  # 最大扫描深度
  max_scan_depth: 3

# 日志配置
logging:
  # 是否记录路径解析详情
  log_path_resolution: true
  
  # 是否记录文件扫描详情
  log_file_scanning: false
  
  # 路径不存在时的日志级别
  missing_path_log_level: "WARNING"
