# 自适应阈值配置文件
# 定义不同组件和指标的阈值计算策略

# 全局配置
global:
  lookback_days: 7          # 历史数据回看天数
  cache_duration_hours: 1   # 阈值缓存时间（小时）
  min_data_points: 10       # 最少数据点要求

# 组件特定配置
components:
  # TiKV组件
  tikv:
    # CPU使用率
    cpu_usage:
      strategy: percentile
      percentile: 0.95
      min_threshold: 0.8      # 80%
      max_threshold: 0.95     # 95%
      safety_margin: 1.1      # 安全边界
      
    # 内存使用量
    memory_usage:
      strategy: percentile
      percentile: 0.90
      min_threshold: **********    # 1GB
      max_threshold: 8589934592    # 8GB
      safety_margin: 1.2
      
    # IO利用率
    io_util:
      strategy: percentile
      percentile: 0.95
      min_threshold: 0.7      # 70%
      max_threshold: 0.95     # 95%
      safety_margin: 1.1
      
    # QPS
    qps:
      strategy: statistical
      sigma: 3                # 3σ原则
      min_threshold: 100
      max_threshold: 10000
      
    # gRPC QPS
    grpc_qps:
      strategy: statistical
      sigma: 3
      min_threshold: 50
      max_threshold: 5000

    # 🔧 新增：TiKV存储相关指标
    # Region待处理数量
    region_pending:
      strategy: statistical
      sigma: 2                # 2σ原则，更敏感
      min_threshold: 1000     # 1000个region
      max_threshold: 500000   # 50万个region
      safety_margin: 1.5

    # 存储大小 (字节)
    store_size:
      strategy: percentile
      percentile: 0.95
      min_threshold: **********      # 1GB
      max_threshold: 1099511627776   # 1TB
      safety_margin: 1.2

    # 存储大小 (字节) - 别名
    storage_size:
      strategy: percentile
      percentile: 0.95
      min_threshold: **********      # 1GB
      max_threshold: 1099511627776   # 1TB
      safety_margin: 1.2

    # 读取吞吐量 (MB/s)
    read_mbps:
      strategy: percentile
      percentile: 0.90
      min_threshold: 104857600       # 100MB/s
      max_threshold: **********0     # 10GB/s
      safety_margin: 1.3

    # WAL写入吞吐量 (MB/s)
    write_wal_mbps:
      strategy: percentile
      percentile: 0.90
      min_threshold: 52428800        # 50MB/s
      max_threshold: 5368709120      # 5GB/s
      safety_margin: 1.3

  # PD组件
  pd:
    # CPU使用率
    cpu_usage:
      strategy: percentile
      percentile: 0.95
      min_threshold: 0.8
      max_threshold: 0.95
      
    # 内存使用量
    memory_usage:
      strategy: percentile
      percentile: 0.90
      min_threshold: 536870912     # 512MB
      max_threshold: 4294967296    # 4GB
      safety_margin: 1.2
      
    # 异常Region数量
    abnormal_region_count:
      strategy: statistical
      sigma: 2                # 2σ原则，更敏感
      min_threshold: 5
      max_threshold: 1000

    # 🔧 新增：PD相关指标
    # Region总数
    region_count:
      strategy: statistical
      sigma: 2
      min_threshold: 100      # 100个region
      max_threshold: 100000   # 10万个region
      safety_margin: 1.2

    # 存储节点数量
    store_up_count:
      strategy: statistical
      sigma: 1                # 1σ原则，非常敏感
      min_threshold: 1        # 至少1个存储节点
      max_threshold: 100      # 最多100个存储节点
      safety_margin: 1.0

    # 不健康存储节点数量
    store_unhealth_count:
      strategy: statistical
      sigma: 1                # 1σ原则，非常敏感
      min_threshold: 0        # 0个不健康节点
      max_threshold: 10       # 最多10个不健康节点
      safety_margin: 1.0

    # 存储大小 (字节)
    storage_size:
      strategy: percentile
      percentile: 0.95
      min_threshold: **********      # 1GB
      max_threshold: 1099511627776   # 1TB
      safety_margin: 1.2

  # TiDB组件
  tidb:
    # 连接数
    connection_count:
      strategy: percentile
      percentile: 0.95
      min_threshold: 100
      max_threshold: 2000
      
    # QPS
    qps:
      strategy: statistical
      sigma: 3
      min_threshold: 100
      max_threshold: 10000
      
    # 延迟指标
    duration_99th:
      strategy: percentile
      percentile: 0.95
      min_threshold: 100000    # 100ms in μs
      max_threshold: 10000000  # 10s in μs
      
    duration_95th:
      strategy: percentile
      percentile: 0.95
      min_threshold: 50000     # 50ms in μs
      max_threshold: 5000000   # 5s in μs
      
    duration_avg:
      strategy: percentile
      percentile: 0.95
      min_threshold: 10000     # 10ms in μs
      max_threshold: 1000000   # 1s in μs

# 指标类型默认策略
metric_type_defaults:
  cpu:
    strategy: percentile
    percentile: 0.95
    min_threshold: 0.8
    max_threshold: 0.95
    
  memory:
    strategy: percentile
    percentile: 0.90
    safety_margin: 1.2
    
  io:
    strategy: percentile
    percentile: 0.95
    min_threshold: 0.7
    max_threshold: 0.95
    
  qps:
    strategy: statistical
    sigma: 3
    min_threshold: 100
    
  latency:
    strategy: percentile
    percentile: 0.95
    min_threshold: 100000    # 100ms in μs
    
  error:
    strategy: percentile
    percentile: 0.99
    min_threshold: 0.01      # 1%
    max_threshold: 0.1       # 10%

# 特殊规则
special_rules:
  # 业务时间规则
  business_hours:
    enabled: true
    start_hour: 9
    end_hour: 18
    timezone: "Asia/Shanghai"
    multiplier: 1.2          # 业务时间阈值提高20%
    
  # 周末规则
  weekend:
    enabled: true
    multiplier: 0.8          # 周末阈值降低20%
    
  # 节假日规则
  holiday:
    enabled: false
    multiplier: 0.6          # 节假日阈值降低40%

# 回退策略
fallback:
  # 当自适应阈值计算失败时使用的默认值
  default_thresholds:
    cpu_usage: 80.0
    memory_usage: 2147483648  # 2GB
    io_util: 80.0
    qps: 10000               # 🔧 修复：QPS阈值从1000提高到10000
    latency: 1000000         # 1s in μs
    error_ratio: 0.05        # 5%
    # 🔧 新增：请求/响应类指标阈值
    request: 10000           # 请求数量阈值
    response: 10000          # 响应数量阈值
    # 🔧 新增：文件系统I/O类指标阈值 - 针对Redis等数据库组件调整
    fs_writes_bytes: 10485760  # 10MB (Redis等数据库组件的持久化写入是正常行为)
    fs_reads_bytes: 10485760   # 10MB
    # 🔧 新增：网络相关指标阈值
    network_packets: 100000    # 10万包
    network_bytes: 104857600   # 100MB
    # 🔧 新增：进程相关指标阈值
    processes: 1000            # 1000个进程
    # 🔧 新增：TCP连接相关指标阈值
    tcp_inuse: 10000          # 1万个TCP连接
