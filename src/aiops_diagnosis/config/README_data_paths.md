# AIOps数据路径配置说明

## 📁 **概述**

AIOps系统现在支持配置化的数据路径管理，不再依赖硬编码的`/data/data`路径。所有数据路径都可以通过配置文件或环境变量进行自定义。

## 🔧 **配置文件**

### **主配置文件**: `config/data_paths.yaml`

```yaml
# 主数据根目录
data_root: "/data/data"

# 备用数据路径（按优先级排序）
fallback_paths:
  - "/data/data"
  - "/opt/aiops/data"
  - "/home/<USER>/aiops"
  - "./data"

# 环境变量覆盖配置
environment_overrides:
  data_root_env: "AIOPS_DATA_ROOT"
  logs_env: "AIOPS_LOGS_PATH"
  traces_env: "AIOPS_TRACES_PATH"
  metrics_env: "AIOPS_METRICS_PATH"
```

## 🌍 **环境变量配置**

### **主要环境变量**

| 环境变量 | 说明 | 示例 |
|---------|------|------|
| `AIOPS_DATA_ROOT` | 主数据根目录 | `/custom/data/path` |
| `AIOPS_LOGS_PATH` | 日志数据路径 | `/custom/logs` |
| `AIOPS_TRACES_PATH` | 调用链数据路径 | `/custom/traces` |
| `AIOPS_METRICS_PATH` | 指标数据路径 | `/custom/metrics` |

### **使用示例**

```bash
# 设置自定义数据根目录
export AIOPS_DATA_ROOT="/opt/aiops/data"

# 设置特定的日志路径
export AIOPS_LOGS_PATH="/var/log/aiops"

# 运行AIOps系统
python a2a_batch_diagnosis.py -i input.json -o output.jsonl --f jsonl
```

## 🔍 **路径优先级**

系统按以下优先级选择数据路径：

1. **环境变量** (最高优先级)
2. **配置文件主路径**
3. **配置文件备用路径** (按顺序尝试)
4. **默认路径** (最后选择)

## 📊 **目录结构**

配置化后的目录结构保持不变：

```
{data_root}/
├── log-parquet/                    # 日志数据
│   └── log_filebeat-server_*.parquet
├── trace-parquet/                  # 调用链数据
│   └── trace_jaeger-span_*.parquet
└── metric-parquet/                 # 指标数据
    ├── apm/
    │   ├── pod/                    # APM Pod指标
    │   └── service/                # APM Service指标
    ├── infra/                      # 基础设施指标
    │   ├── infra_node/
    │   └── infra_pod/
    └── other/                      # 其他组件指标
```

## 💻 **编程接口**

### **Python API**

```python
from aiops_diagnosis.config.data_path_manager import get_data_path_manager

# 获取数据路径管理器
manager = get_data_path_manager()

# 获取各种路径
data_root = manager.get_data_root()
logs_path = manager.get_logs_path()
traces_path = manager.get_traces_path()
apm_pod_path = manager.get_metrics_path("apm", "pod")
infra_node_path = manager.get_metrics_path("infrastructure", "node")

# 便捷函数
from aiops_diagnosis.config.data_path_manager import (
    get_data_root, get_logs_path, get_traces_path, get_metrics_path
)

data_root = get_data_root()
```

### **在Agent中使用**

```python
# 旧方式（硬编码）
class MyAgent:
    def __init__(self):
        self.data_root = "/data/data"  # ❌ 硬编码

# 新方式（配置化）
from ..config.data_path_manager import get_data_path_manager

class MyAgent:
    def __init__(self, data_root_path=None):
        if data_root_path is None:
            data_root_path = get_data_path_manager().get_data_root()  # ✅ 配置化
        self.data_root = data_root_path
```

## 🧪 **测试和验证**

### **测试配置是否生效**

```bash
cd aiops_diagnosis

# 测试默认配置
python3 -c "
from config.data_path_manager import get_data_path_manager
manager = get_data_path_manager()
print(f'数据根目录: {manager.get_data_root()}')
print(f'日志路径: {manager.get_logs_path()}')
"

# 测试环境变量覆盖
AIOPS_DATA_ROOT="/custom/path" python3 -c "
from config.data_path_manager import get_data_path_manager
print(f'自定义路径: {get_data_path_manager().get_data_root()}')
"
```

### **验证目录结构**

```python
from aiops_diagnosis.config.data_path_manager import get_data_path_manager

manager = get_data_path_manager()
success = manager.validate_data_structure()
print(f"目录结构验证: {'✅ 通过' if success else '❌ 失败'}")
```

## 🔧 **迁移指南**

### **从硬编码迁移到配置化**

1. **更新代码中的硬编码路径**：
   ```python
   # 旧代码
   data_root = "/data/data"
   
   # 新代码
   from aiops_diagnosis.config.data_path_manager import get_data_root
   data_root = get_data_root()
   ```

2. **更新类构造函数**：
   ```python
   # 旧代码
   def __init__(self, data_root_path="/data/data"):
   
   # 新代码
   def __init__(self, data_root_path=None):
       if data_root_path is None:
           data_root_path = get_data_root()
   ```

3. **设置环境变量**（可选）：
   ```bash
   export AIOPS_DATA_ROOT="/your/custom/path"
   ```

## 🚀 **最佳实践**

1. **生产环境**：使用环境变量设置数据路径
2. **开发环境**：修改配置文件或使用默认路径
3. **测试环境**：使用相对路径`./data`进行本地测试
4. **容器化部署**：通过环境变量或挂载卷配置路径

## ⚠️ **注意事项**

1. **路径权限**：确保AIOps进程对数据目录有读写权限
2. **路径存在性**：系统会自动验证路径是否存在
3. **备用路径**：配置多个备用路径以提高系统可靠性
4. **缓存机制**：路径解析结果会被缓存，提高性能

## 🔍 **故障排除**

### **常见问题**

1. **路径不存在**：
   ```
   ⚠️ 数据目录不存在: /data/data
   ```
   **解决方案**：检查路径是否正确，或设置`create_missing: true`

2. **权限不足**：
   ```
   ⚠️ 数据目录权限不足: /data/data
   ```
   **解决方案**：`chmod 755 /data/data`

3. **环境变量未生效**：
   **解决方案**：确保环境变量在Python进程启动前设置

### **调试命令**

```bash
# 检查当前配置
python3 -c "
from aiops_diagnosis.config.data_path_manager import get_data_path_manager
manager = get_data_path_manager()
config = manager.get_config()
print(f'配置: {config}')
"

# 验证所有路径
python3 -c "
from aiops_diagnosis.config.data_path_manager import get_data_path_manager
manager = get_data_path_manager()
print(f'数据根目录: {manager.get_data_root()}')
print(f'验证结果: {manager.validate_data_structure()}')
"
```
