"""
AIOps系统统一并发配置管理
支持全局并发控制、Agent级别配置、段大小设置等
"""

from dataclasses import dataclass
from typing import Dict, Any, List, Optional

@dataclass
class ConcurrencyConfig:
    """统一并发配置管理 - 包含段大小配置"""
    
    # === 全局并发控制 ===
    global_max_concurrent: int = 50  # 全局最大并发数
    enable_cross_agent_parallel: bool = True  # 是否启用跨Agent并行执行
    
    # === MetricAgent配置 ===
    metric_agent_concurrent: int = 30  # MetricAgent文件分析并发数
    metric_agent_batch_size: int = 50  # MetricAgent批处理大小
    metric_agent_enable_batching: bool = True  # 是否启用分批处理
    
    # === TraceAgent配置 ===
    trace_agent_concurrent: int = 20   # TraceAgent段分析并发数
    trace_agent_segment_size: int = 2000  # 每段包含的trace记录数
    trace_agent_max_segments: int = 50    # 最大段数限制
    trace_agent_enable_segment_parallel: bool = True  # 启用段级并发
    
    # === LogAgent配置 ===
    log_agent_concurrent: int = 25     # LogAgent段分析并发数
    log_agent_segment_size: int = 1000  # 每段包含的log记录数
    log_agent_max_segments: int = 100   # 最大段数限制
    log_agent_enable_segment_parallel: bool = True  # 启用段级并发
    
    # === LLM调用配置 === 🚨 紧急提升超时阈值
    llm_max_concurrent: int = 20       # 减少LLM调用最大并发数，避免过载
    llm_timeout_seconds: int = 300     # 提升LLM调用超时时间到5分钟
    llm_retry_attempts: int = 2        # 减少LLM调用重试次数
    
    # === 数据加载配置 ===
    dataloader_concurrent: int = 10    # DataLoader文件加载并发数
    dataloader_chunk_size: int = 5000  # 数据加载块大小
    
    # === 智能分段策略 ===
    enable_adaptive_segmentation: bool = True  # 自适应分段
    min_segment_size: int = 500        # 最小段大小
    max_segment_size: int = 5000       # 最大段大小
    segment_overlap_ratio: float = 0.1  # 段重叠比例（用于边界处理）
    
    # === 性能调优参数 ===
    enable_adaptive_concurrency: bool = True  # 自适应并发调整
    memory_threshold_mb: int = 8192    # 内存阈值，超过时降低并发
    cpu_threshold_percent: int = 80    # CPU阈值，超过时降低并发
    
    # === 采样策略配置 ===
    enable_intelligent_sampling: bool = False  # 智能采样（谨慎使用）
    sampling_ratio: float = 0.8       # 采样比例
    priority_components: Optional[List[str]] = None   # 优先处理的组件列表

    def __post_init__(self):
        if self.priority_components is None:
            self.priority_components = [
                "paymentservice", "checkoutservice", "frontend", 
                "cartservice", "productcatalogservice"
            ]

    @classmethod
    def get_optimized_config(cls, performance_mode: str = "balanced") -> "ConcurrencyConfig":
        """获取优化配置"""
        if performance_mode == "fast":
            return cls(
                # 高并发配置
                global_max_concurrent=80,
                metric_agent_concurrent=50,
                metric_agent_batch_size=30,  # 减小批次，增加并行度
                
                trace_agent_concurrent=30,
                trace_agent_segment_size=1500,  # 减小段大小，增加并发
                
                log_agent_concurrent=35,
                log_agent_segment_size=800,   # 减小段大小，增加并发
                
                llm_max_concurrent=60,
                
                # 启用所有优化
                enable_adaptive_segmentation=True,
                enable_adaptive_concurrency=True
            )
        elif performance_mode == "conservative":
            return cls(
                # 保守配置
                global_max_concurrent=20,
                metric_agent_concurrent=10,
                metric_agent_batch_size=80,  # 大批次，减少并发压力
                
                trace_agent_concurrent=8,
                trace_agent_segment_size=3000,  # 大段大小，减少LLM调用
                
                log_agent_concurrent=10,
                log_agent_segment_size=1500,   # 大段大小，减少LLM调用
                
                llm_max_concurrent=15,
                
                # 关闭一些优化以保证稳定性
                enable_adaptive_segmentation=False,
                enable_adaptive_concurrency=False
            )
        elif performance_mode == "memory_optimized":
            return cls(
                # 内存优化配置
                global_max_concurrent=40,
                metric_agent_concurrent=25,
                metric_agent_batch_size=20,  # 小批次，减少内存占用
                
                trace_agent_concurrent=15,
                trace_agent_segment_size=1000,  # 小段大小，减少内存
                
                log_agent_concurrent=20,
                log_agent_segment_size=500,    # 小段大小，减少内存
                
                llm_max_concurrent=30,
                memory_threshold_mb=4096,      # 更严格的内存限制
            )
        else:  # balanced
            return cls()

    def get_effective_segment_size(self, data_size: int, agent_type: str) -> int:
        """根据数据大小动态计算有效段大小"""
        if not self.enable_adaptive_segmentation:
            if agent_type == "trace":
                return self.trace_agent_segment_size
            elif agent_type == "log":
                return self.log_agent_segment_size
            else:
                return 1000

        # 自适应分段逻辑
        if agent_type == "trace":
            base_size = self.trace_agent_segment_size
            max_segments = self.trace_agent_max_segments
        elif agent_type == "log":
            base_size = self.log_agent_segment_size
            max_segments = self.log_agent_max_segments
        else:
            return 1000

        # 计算最优段大小
        optimal_size = max(
            self.min_segment_size,
            min(
                self.max_segment_size,
                data_size // max_segments if data_size > max_segments * base_size else base_size
            )
        )

        return optimal_size

    def get_performance_summary(self) -> Dict[str, Any]:
        """获取性能配置摘要"""
        return {
            "performance_profile": {
                "global_concurrent": self.global_max_concurrent,
                "total_llm_concurrent": self.llm_max_concurrent,
                "cross_agent_parallel": self.enable_cross_agent_parallel
            },
            "agent_configs": {
                "metric": {
                    "concurrent": self.metric_agent_concurrent,
                    "batch_size": self.metric_agent_batch_size
                },
                "trace": {
                    "concurrent": self.trace_agent_concurrent,
                    "segment_size": self.trace_agent_segment_size,
                    "max_segments": self.trace_agent_max_segments
                },
                "log": {
                    "concurrent": self.log_agent_concurrent,
                    "segment_size": self.log_agent_segment_size,
                    "max_segments": self.log_agent_max_segments
                }
            },
            "optimizations": {
                "adaptive_segmentation": self.enable_adaptive_segmentation,
                "adaptive_concurrency": self.enable_adaptive_concurrency,
                "intelligent_sampling": self.enable_intelligent_sampling
            }
        }
