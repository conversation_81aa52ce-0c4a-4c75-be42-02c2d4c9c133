# Trace Analysis Configuration
# 动态配置文件，支持不同服务和场景的自定义阈值

# 全局默认阈值
default_thresholds:
  duration_threshold: 5000  # 默认延迟阈值(ms)
  error_duration: 10000     # 错误延迟阈值(ms)
  timeout_threshold: 30000  # 超时阈值(ms)
  
# 服务特定阈值配置
service_thresholds:
  # 核心业务服务 - 更严格的阈值
  frontend:
    duration_threshold: 2000
    error_duration: 5000
    timeout_threshold: 15000
    criticality: "critical"
    
  checkoutservice:
    duration_threshold: 3000
    error_duration: 8000
    timeout_threshold: 20000
    criticality: "critical"
    
  paymentservice:
    duration_threshold: 2500
    error_duration: 6000
    timeout_threshold: 15000
    criticality: "critical"
    
  # 产品服务 - 中等阈值
  productcatalogservice:
    duration_threshold: 4000
    error_duration: 10000
    timeout_threshold: 25000
    criticality: "high"
    
  cartservice:
    duration_threshold: 3500
    error_duration: 8000
    timeout_threshold: 20000
    criticality: "high"
    
  # 推荐和广告服务 - 相对宽松
  recommendationservice:
    duration_threshold: 6000
    error_duration: 15000
    timeout_threshold: 30000
    criticality: "medium"
    
  adservice:
    duration_threshold: 8000
    error_duration: 20000
    timeout_threshold: 40000
    criticality: "medium"
    
  # 支持服务 - 最宽松
  currencyservice:
    duration_threshold: 5000
    error_duration: 12000
    timeout_threshold: 25000
    criticality: "low"
    
  shippingservice:
    duration_threshold: 5000
    error_duration: 12000
    timeout_threshold: 25000
    criticality: "low"
    
  emailservice:
    duration_threshold: 10000
    error_duration: 25000
    timeout_threshold: 60000
    criticality: "low"

# 服务名称映射配置
service_mapping:
  # 标准化服务名称映射
  "frontend": "frontend"
  "front-end": "frontend"
  "web": "frontend"
  
  "checkout": "checkoutservice"
  "checkout-service": "checkoutservice"
  "checkoutservice": "checkoutservice"
  
  "payment": "paymentservice"
  "payment-service": "paymentservice"
  "paymentservice": "paymentservice"
  
  "cart": "cartservice"
  "cart-service": "cartservice"
  "cartservice": "cartservice"
  
  "catalog": "productcatalogservice"
  "product-catalog": "productcatalogservice"
  "productcatalogservice": "productcatalogservice"
  
  "recommendation": "recommendationservice"
  "recommend": "recommendationservice"
  "recommendationservice": "recommendationservice"
  
  "ad": "adservice"
  "ads": "adservice"
  "adservice": "adservice"
  
  "currency": "currencyservice"
  "currencyservice": "currencyservice"
  
  "shipping": "shippingservice"
  "shippingservice": "shippingservice"
  
  "email": "emailservice"
  "emailservice": "emailservice"
  
  # Redis服务映射
  "redis": "redis-cart"
  "redis-cart": "redis-cart"

# LLM配置 - 使用统一配置
llm_config:
  timeout_seconds: 30
  max_retries: 1  # 统一为1次重试
  retry_delay: 1.0
  
  # JSON解析配置
  json_extraction:
    patterns:
      - r"```json\s*(.*?)\s*```"
      - r"```\s*(.*?)\s*```"
      - r"\{.*\}"
    fallback_enabled: true
    
  # 采样配置
  sampling:
    max_samples: 50
    strategy: "intelligent"  # "intelligent" | "random" | "fixed"
    include_extremes: true
    extreme_percentiles: [0.01, 0.99]  # 包含1%和99%分位数的样本

# 数据校验配置
data_validation:
  required_columns:
    - "duration"
  optional_columns:
    - "service"
    - "status"
    - "startTime"
    - "traceID"
    - "process"
  
  # 数据质量检查
  quality_checks:
    min_duration: 0
    max_duration: 600000  # 10分钟
    valid_statuses: ["ok", "error", "failed", "timeout"]

# 性能优化配置
performance:
  # DataFrame操作优化
  use_vectorization: true
  chunk_size: 10000
  parallel_processing: false
  
  # 内存管理
  memory_limit_mb: 1024
  gc_frequency: 1000  # 每处理1000条记录进行一次垃圾回收

# 日志配置
logging:
  level: "INFO"
  detailed_errors: true
  performance_metrics: true
