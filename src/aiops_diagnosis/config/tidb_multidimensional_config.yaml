# TiDB集群多维度阈值配置文件
# 定义TiDB、PD、TiKV组件的多维度异常检测规则

# 全局配置
global:
  lookback_days: 7
  cache_duration_hours: 24
  anomaly_score_threshold: 0.3
  correlation_threshold: 0.7

# TiDB集群组件定义
components:
  # TiDB组件（SQL层）
  tidb:
    categories:
      performance:
        metrics: [qps, duration_avg, duration_95th, duration_99th]
        weight: 0.4
        description: "TiDB性能指标"
        
      resource:
        metrics: [connection_count, block_cache_size]
        weight: 0.3
        description: "TiDB资源使用"
        
      error:
        metrics: [failed_query_ops, slow_query]
        weight: 0.3
        description: "TiDB错误指标"
    
    # 指标特定配置
    metric_configs:
      qps:
        threshold_method: statistical
        sigma: 3
        min_threshold: 100
        max_threshold: 10000
        
      duration_avg:
        threshold_method: percentile
        percentile: 0.95
        min_threshold: 10000  # 10ms
        max_threshold: 1000000  # 1s
        
      duration_95th:
        threshold_method: percentile
        percentile: 0.90
        min_threshold: 50000  # 50ms
        max_threshold: 5000000  # 5s
        
      duration_99th:
        threshold_method: percentile
        percentile: 0.90
        min_threshold: 100000  # 100ms
        max_threshold: 10000000  # 10s
        
      connection_count:
        threshold_method: percentile
        percentile: 0.95
        min_threshold: 100
        max_threshold: 2000
        
      failed_query_ops:
        threshold_method: percentile
        percentile: 0.99
        min_threshold: 1
        max_threshold: 1000
        
      slow_query:
        threshold_method: percentile
        percentile: 0.95
        min_threshold: 1
        max_threshold: 100

  # PD组件（调度层）
  pd:
    categories:
      resource:
        metrics: [cpu_usage, memory_usage]
        weight: 0.2
        description: "PD资源使用"

      cluster_health:
        metrics: [abnormal_region_count, region_health, region_count]
        weight: 0.4
        description: "PD集群健康度"

      store_management:
        metrics: [store_up_count, store_down_count, store_unhealth_count, store_slow_count, store_low_space_count]
        weight: 0.25
        description: "PD存储节点管理"

      capacity:
        metrics: [storage_size, storage_capacity, storage_used_ratio]
        weight: 0.1
        description: "PD存储容量"

      leadership:
        metrics: [leader_count, learner_count, witness_count]
        weight: 0.05
        description: "PD角色管理"
    
    metric_configs:
      # 资源指标
      cpu_usage:
        threshold_method: percentile
        percentile: 0.95
        min_threshold: 0.8  # 80%
        max_threshold: 0.95  # 95%

      memory_usage:
        threshold_method: percentile
        percentile: 0.90
        min_threshold: 536870912    # 512MB
        max_threshold: **********   # 2GB

      # 集群健康指标
      abnormal_region_count:
        threshold_method: statistical
        sigma: 2  # 更敏感的检测
        min_threshold: 5
        max_threshold: 1000

      region_health:
        threshold_method: percentile
        percentile: 0.05  # 低分位数，健康度下降时报警
        min_threshold: 50.0  # 健康度低于50为异常
        max_threshold: 100.0
        reverse_logic: true  # 数值越低越异常

      region_count:
        threshold_method: percentile
        percentile: 0.95
        min_threshold: 100
        max_threshold: 2000

      # 存储节点管理指标
      store_up_count:
        threshold_method: fixed
        threshold: 2  # 至少需要2个正常节点
        reverse_logic: true  # 数值越低越异常

      store_down_count:
        threshold_method: fixed
        threshold: 1  # 任何存储节点下线都是异常

      store_unhealth_count:
        threshold_method: fixed
        threshold: 1  # 任何存储节点不健康都是异常

      store_slow_count:
        threshold_method: fixed
        threshold: 1  # 任何存储节点慢都是异常

      store_low_space_count:
        threshold_method: fixed
        threshold: 1  # 任何存储节点空间不足都是异常

      # 存储容量指标
      storage_used_ratio:
        threshold_method: percentile
        percentile: 0.95
        min_threshold: 0.8  # 80%使用率
        max_threshold: 0.95  # 95%使用率

      # 角色管理指标
      leader_count:
        threshold_method: fixed
        threshold: 1  # 至少需要1个Leader
        reverse_logic: true  # 数值越低越异常

  # TiKV组件（存储层）
  tikv:
    categories:
      resource:
        metrics: [memory_usage, cpu_usage, available_size, store_size]
        weight: 0.4
        description: "TiKV资源使用"
        
      performance:
        metrics: [qps, grpc_qps, raft_propose_wait, raft_apply_wait]
        weight: 0.4
        description: "TiKV性能指标"
        
      io:
        metrics: [io_util, write_wal_mbps, snapshot_apply_count]
        weight: 0.2
        description: "TiKV IO指标"
    
    metric_configs:
      memory_usage:
        threshold_method: percentile
        percentile: 0.90
        min_threshold: 1073741824  # 1GB
        max_threshold: 8589934592  # 8GB
        
      cpu_usage:
        threshold_method: percentile
        percentile: 0.95
        min_threshold: 0.8  # 80%
        max_threshold: 0.95  # 95%
        
      qps:
        threshold_method: statistical
        sigma: 3
        min_threshold: 100
        max_threshold: 10000
        
      raft_propose_wait:
        threshold_method: percentile
        percentile: 0.95
        min_threshold: 10000  # 10ms
        max_threshold: 100000  # 100ms
        
      raft_apply_wait:
        threshold_method: percentile
        percentile: 0.95
        min_threshold: 5000   # 5ms
        max_threshold: 50000  # 50ms

# 多维度相关性规则
correlation_rules:
  # TiDB性能下降模式
  tidb_performance_degradation:
    component: tidb
    primary_metrics: [duration_99th, duration_95th]
    secondary_metrics: [qps, connection_count, failed_query_ops]
    correlation_threshold: 0.6
    description: "TiDB性能下降：延迟增加通常伴随QPS变化或错误增加"
    severity_weight: 0.8
    
  # TiDB资源瓶颈模式
  tidb_resource_bottleneck:
    component: tidb
    primary_metrics: [connection_count, block_cache_size]
    secondary_metrics: [duration_avg, slow_query]
    correlation_threshold: 0.5
    description: "TiDB资源瓶颈：连接数或缓存异常导致性能下降"
    severity_weight: 0.6

  # TiKV资源压力模式
  tikv_resource_pressure:
    component: tikv
    primary_metrics: [memory_usage, cpu_usage]
    secondary_metrics: [qps, raft_propose_wait, raft_apply_wait]
    correlation_threshold: 0.6
    description: "TiKV资源压力：资源使用率高导致请求处理延迟"
    severity_weight: 0.7
    
  # TiKV存储压力模式
  tikv_storage_pressure:
    component: tikv
    primary_metrics: [available_size, store_size]
    secondary_metrics: [io_util, write_wal_mbps]
    correlation_threshold: 0.5
    description: "TiKV存储压力：存储空间不足导致IO性能下降"
    severity_weight: 0.6

  # PD集群不稳定模式
  pd_cluster_instability:
    component: pd
    primary_metrics: [abnormal_region_count]
    secondary_metrics: [store_down_count, store_unhealth_count, store_slow_count]
    correlation_threshold: 0.4
    description: "PD集群不稳定：异常Region增加通常伴随存储节点问题"
    severity_weight: 0.9
    
  # PD容量告警模式
  pd_capacity_warning:
    component: pd
    primary_metrics: [storage_size, storage_capacity]
    secondary_metrics: [store_slow_count]
    correlation_threshold: 0.3
    description: "PD容量告警：存储容量接近上限可能导致性能下降"
    severity_weight: 0.5

# 跨组件相关性规则
cross_component_rules:
  # TiDB-TiKV性能联动
  tidb_tikv_performance_link:
    primary_component: tidb
    secondary_component: tikv
    primary_metrics: [duration_99th, qps]
    secondary_metrics: [raft_propose_wait, raft_apply_wait, qps]
    correlation_threshold: 0.5
    description: "TiDB-TiKV性能联动：TiDB延迟增加可能由TiKV Raft延迟导致"
    
  # PD-TiKV集群健康联动
  pd_tikv_health_link:
    primary_component: pd
    secondary_component: tikv
    primary_metrics: [abnormal_region_count, store_down_count]
    secondary_metrics: [memory_usage, cpu_usage, available_size]
    correlation_threshold: 0.4
    description: "PD-TiKV健康联动：PD异常Region可能由TiKV资源问题导致"

# 时序模式检测
temporal_patterns:
  # 性能下降趋势
  performance_degradation_trend:
    components: [tidb, tikv]
    metrics: [duration_avg, raft_propose_wait]
    window_size: 5  # 5个时间点
    trend_threshold: 0.2  # 20%增长趋势
    description: "性能下降趋势检测"
    
  # 资源使用增长趋势
  resource_growth_trend:
    components: [tidb, tikv]
    metrics: [memory_usage, cpu_usage, connection_count]
    window_size: 10  # 10个时间点
    trend_threshold: 0.15  # 15%增长趋势
    description: "资源使用增长趋势检测"

# 业务影响评估权重
business_impact_weights:
  tidb:
    duration_99th: 0.3
    duration_95th: 0.25
    failed_query_ops: 0.2
    qps: 0.15
    connection_count: 0.1
    
  pd:
    abnormal_region_count: 0.4
    store_down_count: 0.3
    store_unhealth_count: 0.2
    region_health: 0.1
    
  tikv:
    raft_propose_wait: 0.25
    raft_apply_wait: 0.25
    memory_usage: 0.2
    cpu_usage: 0.15
    qps: 0.15
