#!/usr/bin/env python3
"""
数据路径管理器
集中管理所有数据路径配置，避免硬编码
"""

import os
import yaml
import logging
from pathlib import Path
from typing import Dict, Any, List, Optional
from dataclasses import dataclass

logger = logging.getLogger(__name__)


@dataclass
class DataPathConfig:
    """数据路径配置类"""
    data_root: str
    fallback_paths: List[str]
    directory_structure: Dict[str, Any]
    environment_overrides: Dict[str, str]
    validation: Dict[str, Any]
    cache: Dict[str, Any]
    performance: Dict[str, Any]
    logging: Dict[str, Any]


class DataPathManager:
    """数据路径管理器 - 单例模式"""
    
    _instance = None
    _config: Optional[DataPathConfig] = None
    
    def __new__(cls):
        if cls._instance is None:
            cls._instance = super().__new__(cls)
        return cls._instance
    
    def __init__(self):
        if self._config is None:
            self._load_config()
    
    def _load_config(self):
        """加载数据路径配置"""
        config_file = Path(__file__).parent / "data_paths.yaml"
        
        try:
            with open(config_file, 'r', encoding='utf-8') as f:
                config_dict = yaml.safe_load(f)
            
            self._config = DataPathConfig(**config_dict)
            logger.info(f"✅ 数据路径配置加载成功: {config_file}")
            
        except Exception as e:
            logger.error(f"❌ 数据路径配置加载失败: {e}")
            # 使用默认配置
            self._config = self._get_default_config()
    
    def _get_default_config(self) -> DataPathConfig:
        """获取默认配置"""
        return DataPathConfig(
            data_root="/data/data",
            fallback_paths=["/data/data", "./data"],
            directory_structure={},
            environment_overrides={},
            validation={"check_existence": True, "create_missing": False},
            cache={"enable_caching": True, "path_cache_ttl": 300},
            performance={"concurrent_scan": True, "max_scan_workers": 4},
            logging={"log_path_resolution": True}
        )
    
    def get_data_root(self) -> str:
        """获取数据根目录路径"""
        # 1. 检查环境变量覆盖
        env_var = self._config.environment_overrides.get("data_root_env", "AIOPS_DATA_ROOT")
        if env_var and os.getenv(env_var):
            data_root = os.getenv(env_var)
            logger.info(f"🔧 使用环境变量 {env_var} 的数据根目录: {data_root}")
            return data_root
        
        # 2. 检查配置文件中的主路径
        data_root = self._config.data_root
        if self._validate_path(data_root):
            return data_root
        
        # 3. 尝试备用路径
        for fallback_path in self._config.fallback_paths:
            if self._validate_path(fallback_path):
                logger.warning(f"⚠️ 主数据路径不可用，使用备用路径: {fallback_path}")
                return fallback_path
        
        # 4. 如果都不可用，返回主路径（让调用者处理错误）
        logger.error(f"❌ 所有数据路径都不可用，返回主路径: {data_root}")
        return data_root
    
    def get_logs_path(self) -> str:
        """获取日志数据路径"""
        env_var = self._config.environment_overrides.get("logs_env", "AIOPS_LOGS_PATH")
        if env_var and os.getenv(env_var):
            return os.getenv(env_var)
        
        data_root = self.get_data_root()
        logs_config = self._config.directory_structure.get("logs", {})
        base_dir = logs_config.get("base_dir", "log-parquet")
        
        return str(Path(data_root) / base_dir)
    
    def get_traces_path(self) -> str:
        """获取调用链数据路径"""
        env_var = self._config.environment_overrides.get("traces_env", "AIOPS_TRACES_PATH")
        if env_var and os.getenv(env_var):
            return os.getenv(env_var)
        
        data_root = self.get_data_root()
        traces_config = self._config.directory_structure.get("traces", {})
        base_dir = traces_config.get("base_dir", "trace-parquet")
        
        return str(Path(data_root) / base_dir)
    
    def get_metrics_path(self, metric_type: str = "apm", sub_type: str = "pod") -> str:
        """获取指标数据路径"""
        env_var = self._config.environment_overrides.get("metrics_env", "AIOPS_METRICS_PATH")
        if env_var and os.getenv(env_var):
            base_path = os.getenv(env_var)
        else:
            base_path = self.get_data_root()
        
        # 构建指标路径
        metrics_config = self._config.directory_structure.get("metrics", {})
        
        if metric_type == "apm":
            apm_config = metrics_config.get("apm", {})
            sub_config = apm_config.get(sub_type, {})
            base_dir = sub_config.get("base_dir", f"metric-parquet/apm/{sub_type}")
        elif metric_type == "infrastructure":
            infra_config = metrics_config.get("infrastructure", {})
            sub_config = infra_config.get(sub_type, {})
            base_dir = sub_config.get("base_dir", f"metric-parquet/infra/infra_{sub_type}")
        elif metric_type == "other":
            other_config = metrics_config.get("other", {})
            base_dir = other_config.get("base_dir", "metric-parquet/other")
        else:
            base_dir = f"metric-parquet/{metric_type}"
        
        return str(Path(base_path) / base_dir)
    
    def get_file_pattern(self, data_type: str, sub_type: str = None) -> str:
        """获取文件命名模式"""
        structure = self._config.directory_structure
        
        if data_type == "logs":
            return structure.get("logs", {}).get("pattern", "log_filebeat-server_{date}_{hour}-00-00.parquet")
        elif data_type == "traces":
            return structure.get("traces", {}).get("pattern", "trace_jaeger-span_{date}_{hour}-00-00.parquet")
        elif data_type == "metrics":
            metrics_config = structure.get("metrics", {})
            if sub_type:
                # 处理嵌套的指标类型
                for main_type in ["apm", "infrastructure", "other"]:
                    if main_type in metrics_config:
                        main_config = metrics_config[main_type]
                        if sub_type in main_config:
                            return main_config[sub_type].get("pattern", "")
            return ""
        
        return ""
    
    def _validate_path(self, path: str) -> bool:
        """验证路径是否有效"""
        if not self._config.validation.get("check_existence", True):
            return True
        
        path_obj = Path(path)
        
        # 检查路径是否存在
        if not path_obj.exists():
            if self._config.validation.get("create_missing", False):
                try:
                    path_obj.mkdir(parents=True, exist_ok=True)
                    logger.info(f"📁 创建数据目录: {path}")
                    return True
                except Exception as e:
                    logger.error(f"❌ 创建目录失败 {path}: {e}")
                    return False
            else:
                return False
        
        # 检查是否为目录
        if not path_obj.is_dir():
            return False
        
        # 检查是否可读写
        if not os.access(path, os.R_OK | os.W_OK):
            logger.warning(f"⚠️ 数据目录权限不足: {path}")
            return False
        
        return True
    
    def validate_data_structure(self) -> bool:
        """验证数据目录结构"""
        data_root = self.get_data_root()
        required_subdirs = self._config.validation.get("required_subdirs", [])
        
        missing_dirs = []
        for subdir in required_subdirs:
            full_path = Path(data_root) / subdir
            if not full_path.exists():
                missing_dirs.append(str(full_path))
        
        if missing_dirs:
            logger.warning(f"⚠️ 缺少必需的子目录: {missing_dirs}")
            return False
        
        logger.info("✅ 数据目录结构验证通过")
        return True
    
    def get_config(self) -> DataPathConfig:
        """获取完整配置"""
        return self._config


# 全局实例
_data_path_manager = None


def get_data_path_manager() -> DataPathManager:
    """获取数据路径管理器实例"""
    global _data_path_manager
    if _data_path_manager is None:
        _data_path_manager = DataPathManager()
    return _data_path_manager


def get_data_root() -> str:
    """便捷函数：获取数据根目录"""
    return get_data_path_manager().get_data_root()


def get_logs_path() -> str:
    """便捷函数：获取日志路径"""
    return get_data_path_manager().get_logs_path()


def get_traces_path() -> str:
    """便捷函数：获取调用链路径"""
    return get_data_path_manager().get_traces_path()


def get_metrics_path(metric_type: str = "apm", sub_type: str = "pod") -> str:
    """便捷函数：获取指标路径"""
    return get_data_path_manager().get_metrics_path(metric_type, sub_type)
