"""
配置加载器 - 支持动态配置加载和验证
"""

import yaml
import os
from typing import Dict, Any, Optional
from pathlib import Path
import logging

class ConfigLoader:
    """配置加载器，支持YAML配置文件的加载和验证"""
    
    def __init__(self, config_dir: Optional[str] = None):
        """
        初始化配置加载器
        
        Args:
            config_dir: 配置文件目录，默认为当前文件所在目录
        """
        if config_dir is None:
            config_dir = Path(__file__).parent
        
        self.config_dir = Path(config_dir)
        self.logger = logging.getLogger(__name__)
        self._cache = {}
    
    def load_config(self, config_name: str, use_cache: bool = True) -> Dict[str, Any]:
        """
        加载配置文件
        
        Args:
            config_name: 配置文件名（不含扩展名）
            use_cache: 是否使用缓存
            
        Returns:
            配置字典
        """
        if use_cache and config_name in self._cache:
            return self._cache[config_name]
        
        config_path = self.config_dir / f"{config_name}.yaml"
        
        if not config_path.exists():
            self.logger.error(f"配置文件不存在: {config_path}")
            return {}
        
        try:
            with open(config_path, 'r', encoding='utf-8') as f:
                config = yaml.safe_load(f)
            
            if use_cache:
                self._cache[config_name] = config
            
            self.logger.info(f"成功加载配置文件: {config_path}")
            return config
            
        except Exception as e:
            self.logger.error(f"加载配置文件失败 {config_path}: {e}")
            return {}
    
    def get_service_threshold(self, config: Dict[str, Any], service_name: str, threshold_type: str) -> float:
        """
        获取服务特定的阈值
        
        Args:
            config: 配置字典
            service_name: 服务名称
            threshold_type: 阈值类型
            
        Returns:
            阈值数值
        """
        # 首先尝试获取服务特定配置
        service_thresholds = config.get('service_thresholds', {})
        if service_name in service_thresholds:
            service_config = service_thresholds[service_name]
            if threshold_type in service_config:
                return service_config[threshold_type]
        
        # 回退到默认配置
        default_thresholds = config.get('default_thresholds', {})
        return default_thresholds.get(threshold_type, 5000)  # 默认5秒
    
    def get_service_mapping(self, config: Dict[str, Any]) -> Dict[str, str]:
        """
        获取服务名称映射
        
        Args:
            config: 配置字典
            
        Returns:
            服务名称映射字典
        """
        return config.get('service_mapping', {})
    
    def normalize_service_name(self, config: Dict[str, Any], raw_service_name: str) -> str:
        """
        标准化服务名称
        
        Args:
            config: 配置字典
            raw_service_name: 原始服务名称
            
        Returns:
            标准化后的服务名称
        """
        service_mapping = self.get_service_mapping(config)
        
        # 直接匹配
        if raw_service_name in service_mapping:
            return service_mapping[raw_service_name]
        
        # 小写匹配
        lower_name = raw_service_name.lower()
        if lower_name in service_mapping:
            return service_mapping[lower_name]
        
        # 部分匹配
        for key, value in service_mapping.items():
            if key in lower_name or lower_name in key:
                return value
        
        return raw_service_name  # 无法映射时返回原名称
    
    def validate_config(self, config: Dict[str, Any], config_type: str = "trace_analysis") -> bool:
        """
        验证配置文件的完整性
        
        Args:
            config: 配置字典
            config_type: 配置类型
            
        Returns:
            验证是否通过
        """
        try:
            if config_type == "trace_analysis":
                # 验证必需的配置节
                required_sections = ['default_thresholds', 'service_mapping', 'llm_config']
                for section in required_sections:
                    if section not in config:
                        self.logger.error(f"缺少必需的配置节: {section}")
                        return False
                
                # 验证默认阈值
                default_thresholds = config['default_thresholds']
                required_thresholds = ['duration_threshold', 'error_duration', 'timeout_threshold']
                for threshold in required_thresholds:
                    if threshold not in default_thresholds:
                        self.logger.error(f"缺少默认阈值配置: {threshold}")
                        return False
                
                # 验证LLM配置
                llm_config = config['llm_config']
                if 'timeout_seconds' not in llm_config:
                    self.logger.error("缺少LLM超时配置")
                    return False
            
            self.logger.info(f"配置验证通过: {config_type}")
            return True
            
        except Exception as e:
            self.logger.error(f"配置验证失败: {e}")
            return False
    
    def reload_config(self, config_name: str) -> Dict[str, Any]:
        """
        重新加载配置文件（清除缓存）
        
        Args:
            config_name: 配置文件名
            
        Returns:
            配置字典
        """
        if config_name in self._cache:
            del self._cache[config_name]
        
        return self.load_config(config_name, use_cache=True)
    
    def get_llm_config(self, config: Dict[str, Any]) -> Dict[str, Any]:
        """
        获取LLM相关配置
        
        Args:
            config: 配置字典
            
        Returns:
            LLM配置字典
        """
        # 使用统一的LLM配置
        from .llm_config import get_unified_llm_config
        default_config = get_unified_llm_config()
        return config.get('llm_config', default_config)
    
    def get_sampling_config(self, config: Dict[str, Any]) -> Dict[str, Any]:
        """
        获取采样配置
        
        Args:
            config: 配置字典
            
        Returns:
            采样配置字典
        """
        llm_config = config.get('llm_config', {})
        return llm_config.get('sampling', {
            'max_samples': 50,
            'strategy': 'intelligent',
            'include_extremes': True,
            'extreme_percentiles': [0.01, 0.99]
        })


# 全局配置加载器实例
_config_loader = None

def get_config_loader() -> ConfigLoader:
    """获取全局配置加载器实例"""
    global _config_loader
    if _config_loader is None:
        _config_loader = ConfigLoader()
    return _config_loader

def load_trace_analysis_config() -> Dict[str, Any]:
    """加载trace分析配置的便捷函数"""
    loader = get_config_loader()
    config = loader.load_config('trace_analysis_config')
    
    if not loader.validate_config(config, 'trace_analysis'):
        logging.warning("配置验证失败，使用默认配置")
        return _get_default_config()
    
    return config

def _get_default_config() -> Dict[str, Any]:
    """获取默认配置"""
    return {
        'default_thresholds': {
            'duration_threshold': 5000,
            'error_duration': 10000,
            'timeout_threshold': 30000
        },
        'service_mapping': {},
        'llm_config': {
            'timeout_seconds': 30,
            'max_retries': 1,  # 统一为1次重试
            'retry_delay': 1.0,
            'sampling': {
                'max_samples': 50,
                'strategy': 'intelligent'
            }
        }
    }
