# Metric阈值配置文件
# 支持全局阈值和对象级别的个性化阈值

# APM指标阈值配置
apm:
  error_ratio: 0.001              # 错误率 > 0.1%
  client_error_ratio: 0.001       # 客户端错误率 > 0.1%
  server_error_ratio: 0.001       # 服务端错误率 > 0.1%
  rrt: 500                        # 响应时间 > 0.5秒
  rrt_max: 1000                   # 最大响应时间 > 1秒

# 基础设施指标阈值配置
infra:
  node_cpu_usage_rate: 80.0       # Node CPU使用率 > 80%
  node_memory_usage_rate: 85.0    # Node内存使用率 > 85%
  node_filesystem_usage_rate: 90.0 # Node磁盘使用率 > 90%
  pod_cpu_usage: 50.0             # Pod CPU使用率 > 50%
  pod_memory_working_set_bytes: 536870912  # Pod内存 > 512MB

# TiDB数据库指标阈值配置
tidb:
  qps: 1000                       # QPS > 1000
  duration_99th: 1000             # 99分位延迟 > 1秒
  duration_95th: 500              # 95分位延迟 > 0.5秒
  failed_query_ops: 10            # 失败查询 > 10
  connection_count: 100           # 连接数 > 100

# 其他组件指标阈值配置
other:
  pd:
    cpu_usage: 80.0               # PD CPU使用率 > 80%
    memory_usage: **********      # PD内存 > 1GB
    store_down_count: 0           # 下线存储节点 > 0
    store_unhealth_count: 0       # 不健康存储节点 > 0
    abnormal_region_count: 10     # 异常Region > 10
  tikv:
    cpu_usage: 80.0               # TiKV CPU使用率 > 80%
    memory_usage: **********      # TiKV内存 > 2GB
    io_util: 80.0                 # IO使用率 > 80%
    qps: 5000                     # QPS > 5000
    grpc_qps: 1000                # gRPC QPS > 1000

# 对象级别的个性化阈值配置
objects:
  # 为特定服务设置个性化阈值
  frontend:
    rrt: 300                      # frontend服务响应时间阈值更严格
    error_ratio: 0.0005           # frontend服务错误率阈值更严格
  
  # 为特定Pod设置个性化阈值
  frontend-1:
    pod_cpu_usage: 60.0           # frontend-1 Pod的CPU阈值更高
    
  # 为特定Node设置个性化阈值
  aiops-k8s-01:
    node_cpu_usage_rate: 85.0     # 主节点CPU阈值更高
    node_memory_usage_rate: 90.0  # 主节点内存阈值更高

# 动态阈值计算配置
dynamic_threshold:
  enabled: true                   # 是否启用动态阈值
  min_data_points: 10             # 最少数据点数量
  default_method: "percentile"    # 默认计算方法: percentile, statistical, iqr
  percentile_value: 0.95          # 分位数方法的分位数值
  sigma_multiplier: 3             # 统计方法的标准差倍数
  iqr_multiplier: 1.5             # IQR方法的倍数

# 异常检测配置
anomaly_detection:
  severity_thresholds:
    critical: 5.0                 # 超过阈值5倍为严重
    high: 2.0                     # 超过阈值2倍为高
    medium: 1.5                   # 超过阈值1.5倍为中等
  min_anomaly_count: 1            # 最少异常数据点
  max_retries: 3                  # 最大重试次数
  timeout_seconds: 30             # 超时时间
