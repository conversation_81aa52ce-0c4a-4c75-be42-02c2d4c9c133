# This workflow will build a .NET project
# For more information see: https://docs.github.com/en/actions/automating-builds-and-tests/building-and-testing-net

name: dotnet-release

on:
  workflow_dispatch:
  push:
    branches:
      - release/dotnet/**

concurrency:
  group: ${{ github.workflow }}-${{ github.ref }}-${{ github.head_ref }}
  cancel-in-progress: true

permissions:
  contents: read
  packages: write

jobs:
  build:
    name: Build and release
    runs-on: ubuntu-latest
    environment: dotnet
    defaults:
      run:
        working-directory: dotnet
    steps:
    - uses: actions/checkout@v4
      with:
        lfs: true
    - name: Set up Python 3.11
      uses: actions/setup-python@v5
      with:
        python-version: 3.11
    - name: Install jupyter and ipykernel
      run: |
        python -m pip install --upgrade pip
        python -m pip install jupyter
        python -m pip install ipykernel
    - name: list available kernels
      run: |
        python -m jupyter kernelspec list
    - name: Setup .NET
      uses: actions/setup-dotnet@v4
      with:
        dotnet-version: '8.0.x'
    - name: Restore dependencies
      run: |
        dotnet restore -bl
    - name: Build
      run: |
        echo "Build AutoGen"
        dotnet build --no-restore --configuration Release -bl /p:SignAssembly=true
    - name: Unit Test
      run: dotnet test --no-build -bl --configuration Release
      env:
        AZURE_OPENAI_API_KEY: ${{ secrets.AZURE_OPENAI_API_KEY }}
        AZURE_OPENAI_ENDPOINT: ${{ secrets.AZURE_OPENAI_ENDPOINT }}
        AZURE_GPT_35_MODEL_ID: ${{ secrets.AZURE_GPT_35_MODEL_ID }}
        OEPNAI_API_KEY: ${{ secrets.OPENAI_API_KEY }}
    - name: Pack
      run: |
        echo "Create release build package"
        dotnet pack --no-build --configuration Release --output './output/release' -bl

        echo "ls output directory"
        ls -R ./output
    - name: Publish package to Nuget
      run: |
        echo "Publish package to Nuget"
        echo "ls output directory"
        ls -R ./output/release
        # remove AutoGen.SourceGenerator.snupkg because it's an empty package
        rm ./output/release/AutoGen.SourceGenerator.*.snupkg
        dotnet nuget push --api-key ${{ secrets.AUTOGEN_NUGET_API_KEY }} --source https://api.nuget.org/v3/index.json ./output/release/*.nupkg --skip-duplicate
