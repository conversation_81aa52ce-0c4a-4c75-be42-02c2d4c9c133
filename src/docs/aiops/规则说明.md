input.json示例

[
    {
        "uuid": "33c11d00-2",
        "Anomaly Description": "The system experienced an anomaly from 2025-04-26T19:10:15Z to 2025-04-26T19:40:15Z. Please infer the possible cause."
    },
    {
        "uuid": "2f9d1c3d-28",
        "Anomaly Description": "The system experienced an anomaly from 2025-04-29T06:10:42Z to 2025-04-29T06:38:42Z. Please infer the possible cause."
    }
]
参考范例
输入：

1. 故障案例的uuid以及对应的一段自然语言查询（如：“The system experienced an anomaly from 2025-04-30T19:10:15Z to 2025-04-30T19:40:15Z. Please infer the possible cause.”）；
2. 与之关联的运行数据，包括：
1）监控指标（Metrics）：如 CPU 使用率、磁盘 I/O 等；
2）服务日志（Logs）：组件产生的结构化或半结构化日志；
3）调用链数据（Traces）：服务之间的依赖与请求路径；

输出：

结构化 JSON 格式的根因结果（字段组合视任务而定）：

{
  "uuid": "33c11d00-2",
  "component": "checkoutservice",
  "reason": "disk IO overload",
  "time": "2025-04-21 12:18:00",
  "reasoning_trace": [
    {
      "step": 1,
      "action": "LoadMetrics(checkoutservice)",
      "observation": "disk_read_latency spike"
    },
    {
      "step": 2,
      "action": "TraceAnalysis('frontend -> checkoutservice')",
      "observation": "checkoutservice self-loop spans"
    },
    {
      "step": 3,
      "action": "LogSearch(checkoutservice)",
      "observation": "IOError in 3 logs"
    }
  ]
}
字段说明：

字段名	类型	是否必须	说明
uuid	string	是	该条返回结果所对应的故障案例的uuid
component	string	是	根因组件的名称，每条样本只评估一个根因组件，若提交多个组件，仅评估 JSON 中首个出现的 component 字段，类型需为 string。
reason	string	是	故障发生的原因或类型，如果超出20个单词将被截断，仅保留前20个单词参与评分
time	string(ISO)	是	根因事件对应的时间点，用于任务对齐与人工验证，不参与自动评分。建议精度在分钟级别，并尽量贴近指标或日志异常的时间点。
reasoning_trace	object[]	是	完整推理轨迹，包含每步 action/observation 等，其中observation 超出 20 个单词将被截断，仅保留前 20 词参与评分
注1： 字段格式说明：

"time" 字段推荐格式为 "YYYY-MM-DD HH:mm:ss"，例如 "2025-04-21 12:18:00"；
"reasoning_trace" 为包含多个 step 对象的数组，每个对象应包含以下字段：
step：整数，表示推理步骤编号（从 1 开始）；
action：字符串，描述该步调用或操作；
observation：字符串，描述该步观察到的结果，需控制在 20 字内；
所有字段名建议使用 snake_case 命名风格，避免大小写混用。


用例说明
组委会提供 - 参考答案
{
  "uuid": "33c11d00-2",
  "component": "checkoutservice",
  "reason": "disk IO overload",
  "time": "2025-04-21 12:18:00",
  "reasoning_trace":  [
  {
    "step": 1,
    "action": "LoadMetrics(checkoutservice",
    "observation": "disk_read_latency spike observed at 12:18"
  },
  {
    "step": 2,
    "action": "TraceAnalysis('frontend -> checkoutservice')",
    "observation": "checkoutservice appears multiple times in self-loop spans"
  },
  {
    "step": 3,
    "action": "LogSearch(checkoutservice)",
    "observation": "IOError found in 3 log entries"
  }
]
}

选手提交答案 - 评测示例1 <答案正确>
{
  "uuid": "33c11d00-2",
  "component": "checkoutservice",
  "reason": "disk IO overload",
  "time": "2025-04-21 12:18:00",
  "reasoning_trace": [
    {
      "step": 1,
      "action": "QueryMetric(checkoutservice)",
      "observation": "I/O latency peak at 12:18"
    },
    {
      "step": 2,
      "action": "TraceCheck",
      "observation": "checkoutservice is on a self-loop chain"
    },
    {
      "step": 3,
      "action": "LogInspection",
      "observation": "multiple IOError records"
    }
  ]
}

评分解释：

✅ 根因组件正确 → LA = 0.40；

✅ 故障类型正确 → TA = 0.40；

✅ Reasoning_trace 共 3 步，推理紧凑 → Efficiency = 1.00 × 0.10 = 0.10；

✅ 命中 3 个关键证据点（指标、日志、调用链） → Explainability = 0.10；

🔢 总分：0.40 + 0.40 + 0.10 + 0.10 = 1.00

选手提交答案 - 评测示例2 <答案部分正确>
{
  "uuid": "33c11d00-2",
  "component": "checkoutservice",
  "reason": "high latency",
  "time": "2025-04-21 12:18:00",
  "reasoning_trace": [
    {
      "step": 1,
      "action": "FetchMetrics(checkoutservice)",
      "observation": "latency spikes detected"
    },
    {
      "step": 2,
      "action": "LogScan(checkoutservice)",
      "observation": "error logs detected"
    }
  ]
}

评分解释：

✅ 根因组件正确 → LA = 0.40；

❌ 关键词未命中，语义相似度过低 → TA = 0.00；

✅ Reasoning_trace 共 2 步，推理紧凑 → Efficiency = 1.00 × 0.10 = 0.10；

✅ 命中 2 个关键证据点（指标 + 日志），共 3 个 → Explainability = 2/3 × 0.10 ≈ 0.067；

🔢 总分： 0.40 + 0.00 + 0.00 + 0.067 = 0.467

选手提交答案 - 评测示例3 <答案错误>
{
  "uuid": "33c11d00-2",
  "component": "frontend",
  "reason": "network congestion",
  "time": "2025-04-21 12:10:00",
  "reasoning_trace": [
    {
      "step": 1,
      "action": "LoadMetrics(frontend)",
      "observation": "CPU usage stable"
    },
    {
      "step": 2,
      "action": "CheckTraces",
      "observation": "normal communication"
    }
  ]
}

评分解释：

❌ 根因组件错误 → LA = 0.00；

❌ 关键词未命中，语义相似度过低 → TA = 0.00；

❌ 根因未命中 → Efficiency = 0.00（未进入效率评分）；

❌ 未命中任何有效证据（指标正常、调用链正常，均无关键异常） → Explainability = 0.00

🔢 总分： 0.00 + 0.00 + 0.00 + 0.00 = 0.00



Q1: 是否必须使用多智能体架构？
A1: 没有限制。单模型方案（如 ReAct、Chain-of-Thought）亦可，只要能够生成结构化 reasoning_trace 且满足推理完整性要求即可。

Q2: 模型可以使用外部知识或预设规则吗？
A2: 不可使用硬编码规则代替模型判断，但允许将 SOP、组件属性、故障类型等信息嵌入 prompt，前提是推理由 LLM 驱动。

Q3: 是否可以使用缓存或预处理数据？
A3: 允许对原始监控数据进行预处理，但判断、决策、因果分析必须由 LLM 实时完成。

Q4: 如果预测结果中多报了组件，会怎样处理？
A4: 当前评测仅采纳提交结果中的第一个根因（按 JSON 字段顺序），忽略后续条目。

Q5: reasoning_trace 中是否可以合并多步为一步？
A5: 不建议。每步应保持“action–observation”结构，反映真实推理流程，否则会影响可解释性得分。

Q7: 是否支持语言模型调用外部代码工具？
A7: 支持在执行代码模式下调用外部工具模块（如 Python 执行环境），但这些调用需由 LLM 决策控制，不能脱离语言模型主导。

Q8：可以使用外部知识库或训练数据优化模型吗？
A8：允许在不违反公平性前提下使用公开资料和通用知识来增强模型推理能力，但需满足以下要求：

✅ 可使用通用公开数据集、行业技术文档等构建提示模板（prompt）或规则框架；
✅ 如使用外部知识库，请在提交文档中注明来源与用途，供评委核查；
❌ 严禁通过人工标注方式或硬编码答案绕过模型决策，否则视为违规处理。