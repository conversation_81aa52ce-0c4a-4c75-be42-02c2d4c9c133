# 数据说明

系统架构

![系统架构](./image.png)

本届挑战赛使用的系统基于 Google 开源的微服务示例系统 HipsterShop，整体由多个使用不同编程语言实现的微服务组成。上图中的矩形框表示各个微服务，用户通过前端服务（Frontend）访问整个系统。每个微服务对应一个容器级别的监控系统，同时部署这些服务的虚拟机也采集了主机级别的运行数据。

系统采用动态部署架构，共包含 10 个核心微服务和 8 台虚拟机。每个微服务部署了 3 个 Pod，总计 30 个 Pod，这些 Pod 会被动态调度分布到 8 台虚拟机上。此外，系统中的 TiDB 组件也部署在虚拟机上，包括 tidb-tidb、tidb-pd 和 tidb-tikv 三个核心服务，每个服务部署 1 个 Pod。

为构造多样化的故障场景，系统支持三种层级的故障注入：service 级、pod 级和 node 级。其中，service 和 pod 层级的故障主要模拟 Kubernetes 容器层面的异常行为，Service 级故障表示该服务下的所有 3 个 Pod 同时注入故障；Pod 级故障仅对某一个具体的 Pod 注入故障。

这张架构图核心是一条“从用户发起请求，经由 Frontend 再分发到各个微服务，最后再落库／发起下游调用”的调用链，我们可以这样把它拆解成几个步骤来看：

1. **用户 → Frontend**

   * 所有流量都由 Frontend 接入，用户在页面上的每一次点击、每一次操作，Front end 都负责路由到下面的服务。

2. **Frontend → 核心查询服务**

   * **ProductCatalog（商品目录）**：用于查询商品的基本信息（名称、价格、库存等）。
   * **Recommendation（推荐）**：先调用商品目录服务，拉取商品信息后再给用户做个性化推荐。
   * **Ad（广告）**：直接查询底层数据库（TiDB）以获取投放的广告内容。
   * **Currency（汇率）**：实时获取不同货币的汇率，用于商品价格或订单金额的多币种显示。
   * **Shipping（运费）**：计算并返回当前地址/商品组合下的运费。
   * **Cart（购物车）**：将用户的选购行为写入 Redis Cache，以保证高并发下的快速读写。

3. **Fallback 存储 → TiDB + Redis**

   * **TiDB 集群**（内部包含 TiDB-TiDB、TiKV、PD）：

     * 商品目录（ProductCatalog）和广告（Ad）服务都从这里读取核心数据。
   * **Redis Cache**：

     * 购物车（Cart）服务将用户的临时选购状态存入 Redis，以便快速响应和后续结算。

4. **下单流程：Frontend → Checkout**

   * 用户点击“去结算”后，Frontend 调用 **Checkout** 服务，后者负责整个下单编排。

5. **Checkout → 支付 & 发货 & 通知**

   * **Payment（支付）**：发起真正的支付请求（第三方支付网关）。
   * **Currency（汇率）**：再次确认订单金额的币种转换。
   * **Shipping（运费）**：确认最终的运费计算。
   * **Email（邮件）**：下单成功后给用户发送订单确认邮件。

---

### 简要流程图

```
User
  ↓
Frontend ──▶ Ad ──────────▶ TiDB
    │
    ├─▶ Recommendation ──▶ ProductCatalog ──▶ TiDB
    │
    ├─▶ ProductCatalog ──▶ TiDB
    │
    ├─▶ Currency
    │
    ├─▶ Shipping
    │
    ├─▶ Cart ──▶ Redis Cache
    │
    └─▶ Checkout ──▶ { Payment, Currency, Shipping, Email }
```

* **Diagram 要点**

  1. **Fan‑out 模式**：Frontend 一次请求可以并发调用多个微服务。
  2. **服务依赖**：Recommendation→ProductCatalog; Ad/ProductCatalog→TiDB。
  3. **状态存储**：购物车走 Redis，核心业务数据存 TiDB。
  4. **结算编排**：Checkout 汇聚了支付、运费、汇率、邮件通知四个子服务。

这样就能清晰地看出，各个微服务之间谁依赖谁、谁先读库、谁再写库、哪里做缓存、哪里做编排。


## 总体概述

本数据集包含系统采集的三类关键数据：监控指标（Metric）、分布式调用链（Trace）和容器日志（Log）。数据均以 Parquet 格式存储，以便高效读取与分析。整体结构如下：

```text
├── log-parquet
│   ├── log_filebeat-server_2025-05-27_00-00-00.parquet
│   ├── log_filebeat-server_2025-05-27_01-00-00.parquet
│   └── log_filebeat-server_2025-05-27_02-00-00.parquet
├── metric-parquet
│   ├── apm
│   │   ├── pod
│   │   │   └── pod_adservice-0_2025-05-27.parquet
│   │   ├── pod_ns_hipstershop_2025-05-27.parquet
│   │   └── service
│   │       └── service_adservice_2025-05-27.parquet
│   ├── infra
│   │   ├── infra_node
│   │   │   ├── infra_node_node_cpu_usage_rate_2025-05-27.parquet
│   │   │   └── infra_node_node_disk_read_bytes_total_2025-05-27.parquet
│   │   ├── infra_pod
│   │   │   ├── infra_pod_pod_cpu_usage_2025-05-27.parquet
│   │   │   └── infra_pod_pod_fs_reads_bytes_2025-05-27.parquet
│   │   └── infra_tidb
│   │       ├── infra_tidb_block_cache_size_2025-05-27.parquet
│   │       └── infra_tidb_connection_count_2025-05-27.parquet
│   └── other
│       ├── infra_pd_abnormal_region_count_2025-05-27.parquet
│       ├── infra_pd_leader_count_2025-05-27.parquet
│       └── infra_tikv_available_size_2025-05-27.parquet
└── trace-parquet
    ├── trace_jaeger-span_2025-05-01_00-59-00.parquet
    └── trace_jaeger-span_2025-05-01_01-59-00.parquet
```

接下来将分三大部分详细说明各类数据的存储结构与字段含义。

## 数据文件说明

本节介绍三类数据在文件系统中的组织结构及命名规范，帮助快速定位所需文件。

### Metric

Metric 目录下又分为两级子目录：apm/ 与 infra/，其中还包括一个 other/ 目录，用于存放不属于前两者范畴的监控指标。具体结构如下：

```text
├── metric-parquet
│   ├── apm
│   │   ├── pod
│   │   │   └── pod_adservice-0_2025-05-27.parquet
│   │   ├── pod_ns_hipstershop_2025-05-27.parquet
│   │   └── service
│   │       └── service_adservice_2025-05-27.parquet
│   ├── infra
│   │   ├── infra_node
│   │   │   └── infra_node_node_disk_read_bytes_total_2025-05-27.parquet
│   │   ├── infra_pod
│   │   │   └── infra_pod_pod_fs_reads_bytes_2025-05-27.parquet
│   │   └── infra_tidb
│   │       └── infra_tidb_connection_count_2025-05-27.parquet
│   └── other
│       └── infra_tikv_available_size_2025-05-27.parquet
```

APM 指标 （apm/）

- 目录说明

  应用性能监控（APM）指标由 DeepFlow 系统在集群中采集，按照业务命名空间、Pod、Service 分类存储。

  - 根目录下的文件以 {namespace}_{日期}.parquet 命名，表示该业务命名空间中所有对象（Pod 和 Service）在指定日期的 APM 数据。
  - pod/ 子目录：每个 Pod 对应一个 Parquet 文件，格式为 pod_{podName}_{日期}.parquet，其中 podName 为 Pod 的名称（通常带有副本序号）。
  - service/ 子目录：每个 Service 对应一个 Parquet 文件，格式为 service_{serviceName}_{日期}.parquet，serviceName 为 Service 名称。

Infra 指标（infra/）

- 目录说明

  机器性能指标由 Prometheus 采集，细分为三个子目录：infra_node/、infra_pod/ 和 infra_tidb/。

  - infra_node/：Node 级别的指标，如 CPU、内存、磁盘、网络等；文件名称前缀为 infra_node_node_{kpiKey}_{日期}.parquet。
  - infra_pod/：Pod 级别的指标，如 Pod 的 CPU 使用率、内存使用量、文件系统读写、网络吞吐等；文件命名为 infra_pod_{kpiKey}_{日期}.parquet，其中 kpiKey 表示具体的指标编码。
  - infra_tidb/：TiDB 组件相关的指标，如连接数、慢查询、Block Cache 大小等；文件命名为 infra_tidb_{kpiKey}_{日期}.parquet。

Other（其他组件）指标（other/）

- 目录说明

  用于存储集群里其他关键组件的指标。

  - 文件通常以 infra_{component}\_{metricKey}_{日期}.parquet 命名，例如 infra_pd_abnormal_region_count_2025-05-27.parquet 表示 PD（Placement Driver）中异常 Region 数量指标。
  - 其他常见示例：infra_pd_leader_count_2025-05-27.parquet（PD Leader 数量）、infra_tikv_available_size_2025-05-27.parquet（TiKV 可用存储容量）等。

### Trace

Trace 目录下的 Parquet 文件以小时为粒度存储了 Jaeger 采集的调用链信息。结构示例：

```text
├── trace-parquet
    ├── trace_jaeger-span_2025-05-01_00-59-00.parquet
    └── trace_jaeger-span_2025-05-01_01-59-00.parquet
```
- 文件命名 

  格式为 trace_jaeger-span_{日期}_{HH}-59-00.parquet，如 trace_jaeger-span_2025-05-27_13-59-00.parquet 表示该文件包含 2025-05-27 当天 13:00–14:00 期间所有采集到的 Span 信息，时区为 CST。

### Log

Log 目录与 Trace 类似，也以小时为单位分文件，存储 Filebeat 从各 Pod 收集的容器日志。本示例只列举部分文件：

```text
├── log-parquet
│   ├── log_filebeat-server_2025-05-27_00-00-00.parquet
│   ├── log_filebeat-server_2025-05-27_01-00-00.parquet
│   └── log_filebeat-server_2025-05-27_02-00-00.parquet
```

- 文件命名

  格式为 log_filebeat-server_{日期}_{HH}-00-00.parquet，如 log_filebeat-server_2025-05-27_13-59-00.parquet 表示 2025-05-27 13:00–14:00 时间段收集到的所有日志，时区为 CST。

## 数据格式说明

接下来针对各类别数据的字段含义、典型示例及采集粒度进行详细说明，以便后续数据清洗、建模和可视化分析。

### Metric

Metric 数据既包括业务指标（APM）也包括性能指标（Infra），本节分为两部分说明。

#### 业务指标 （APM指标）

APM 指标主要反映业务服务在一定时间窗口内的请求与响应情况，包括错误率、时延等核心指标。主要字段和含义如下表所示。

| 指标编码             | 指标名称       | 单位 | 指标粒度 | 说明 |
| -------------------- | -------------- | ---- | -------- | ---- |
| request              | 请求数量       | 个   | 60       | 请求总数 |
| response             | 响应数量       | 个   | 60       | 响应总数 |
| rrt                  | 平均时延       | 微秒 | 60       | 采集周期内所有应用时延的平均值，单次应用时延等于响应与请求的时间差 |
| rrt_max              | 最大时延       | 微秒 | 60       | 采集周期内所有应用时延的最大值，单次应用时延等于响应与请求的时间差 |
| error                | 异常           | 个   | 60       | 客户端异常 + 服务端异常 |
| client_error         | 客户端异常     | 个   | 60       | 根据具体应用协议的响应码判断异常，不同协议的定义见 l7_flow_log 中 response_status 字段的说明 |
| server_error         | 服务端异常     | 个   | 60       | 根据具体应用协议的响应码判断异常，不同协议的定义见 l7_flow_log 中 response_status 字段的说明 |
| timeout              | 超时           | 个   | 60       | 应用超时的统计次数（默认配置下：TCP 类应用在 1800s 内未采集到响应，UDP 类应用在 150s 内未采集到响应） |
| error_ratio          | 异常比例       | %    | 60       | 异常 / 响应 |
| client_error_ratio   | 客户端异常比例 | %    | 60       | 客户端异常 / 响应 |
| server_error_ratio   | 服务端异常比例 | %    | 60       | 服务端异常 / 响应 |
| timeout_ratio        | 超时比例       | %    | 60       | 超时 / 请求 |


>示例数据（部分）
>| time                  | client_error | client_error_ratio | error | error_ratio | object_id     | object_type | request | response |     rrt | rrt_max | server_error | server_error_ratio | timeout |
>| --------------------- | ------------ | ------------------ | ----- | ----------- | ------------- | ----------- | ------- | -------- | ------- | ------- | ------------ | ------------------ | ------- |
>| 2025-05-05 16:04:00+00:00  | 0            | 0.00               | 0     | 0.00        | adservice-0   | pod         | 325     | 328      | 3661.83 | 43319   | 0            | 0                  | 0       |
>| 2025-05-05 16:04:00+00:00  | 11           | 2.68               | 11    | 2.68        | adservice-0   | pod         | 410     | 411      | 3708.56 | 43864   | 0            | 0                  | 0       |
>| 2025-05-05 16:04:00+00:00  | 0            | 0.00               | 0     | 0.00        | adservice-0   | pod         | 319     | 320      | 4140.42 | 51831   | 0            | 0                  | 0       |
>| 2025-05-05 16:04:00+00:00  | 6            | 1.44               | 6     | 1.44        | adservice-0   | pod         | 412     | 416      | 3401.22 | 43868   | 0            | 0                  | 0       |
>| 2025-05-05 16:04:00+00:00  | 0            | 0.00               | 0     | 0.00        | adservice-0   | pod         | 294     | 296      | 3480.99 | 43820   | 0            | 0                  | 0       |

以上示例展示 adservice-0 Pod 在不同分钟的业务调用情况。当需要计算某个时段的平均时延或错误率，可以按分钟粒度聚合 rrt、error_ratio 等字段。

#### 性能指标（Infra指标）

性能指标反映系统的底层资源使用情况，包括 Pod、Node、TiDB 组件在内的多种对象。下表列出了常见指标编码及其含义（以 Parquet 文件中的 kpi_key 为主）：

| 对象类型 | 指标编码                        | 指标名称                      | 单位 |
| -------- | ------------------------------- | ----------------------------- | ---- |
| pod      | pod_cpu_usage                   | CPU 使用率                    | %    |
| pod      | pod_processes                   | 进程数                        | 个   |
| pod      | pod_memory_working_set_bytes    | 内存使用大小                  | 字节 |
| pod      | pod_fs_writes_bytes             | 写入字节的累积计数            | 字节 |
| pod      | pod_fs_reads_bytes              | 累计读取字节数                | 字节 |
| pod      | pod_network_receive_bytes       | 接收字节的累积计数            | 字节 |
| pod      | pod_network_transmit_bytes      | 传输字节的累积计数            | 字节 |
| pod      | pod_network_receive_packets     | 接收数据包的累积计数          | 个   |
| pod      | pod_network_transmit_packets    | 传输数据包的累积计数          | 个   |
| node     | node_cpu_usage_rate             | CPU 使用率                    | %    |
| node     | node_memory_usage_rate          | 内存使用率                    | %    |
| node     | node_filesystem_usage_rate      | 磁盘使用率                    | %    |
| node     | node_memory_MemAvailable_bytes  | 空闲内存大小                  | 字节 |
| node     | node_memory_MemTotal_bytes      | 内存总大小                    | 字节 |
| node     | node_filesystem_size_bytes      | 存储设备总大小                | 字节 |
| node     | node_filesystem_free_bytes      | 存储设备空闲大小              | 字节 |
| node     | node_disk_read_bytes_total      | 成功读取的字节数              | 字节 |
| node     | node_disk_read_time_seconds_total | 磁盘分区读取花费的秒数       | 秒   |
| node     | node_disk_written_bytes_total   | 成功写入的字节数              | 字节 |
| node     | node_disk_write_time_seconds_total | 磁盘分区写操作花费的秒数   | 秒   |
| node     | node_network_receive_bytes_total | {{device}} 接口接收速率      | 字节/秒 |
| node     | node_network_receive_packets_total | {{device}} 接口每秒接收的数据包总数 | 个/秒 |
| node     | node_network_transmit_bytes_total | {{device}} 接口发送速率      | 字节/秒 |
| node     | node_network_transmit_packets_total | {{device}} 接口每秒发送的数据包总数 | 个/秒 |
| node     | node_sockstat_TCP_inuse         | TCP_inuse – 正在使用（正在侦听）的 TCP 套接字数量 | 个 |
| tidb     | connection_count                | 连接数                        | 个   |
| tidb     | failed_query_ops                | 失败请求数                    | 个   |
| tidb     | duration_99th                   | 99 分位请求延迟               | 微秒 |
| tidb     | duration_95th                   | 95 分位请求延迟               | 微秒 |
| tidb     | duration_avg                    | 平均请求延迟                  | 微秒 |
| tidb     | qps                             | 请求数量                      | 个/秒 |
| tidb     | slow_query                      | 慢查询                        | 个   |
| tidb     | block_cache_size                | Block Cache 大小             | 字节 |


字段释义（infra 样例数据文件）

| 字段名            | 含义                                                         |
| ----------------- | ------------------------------------------------------------ |
| time              | 记录时间（UTC 格式），示例：`2025-05-05 16:04:00+00:00`            |
| cf                | 保留字段，当前为空，可用于后续扩展或标记                   |
| device            | 若指标涉及网络、文件系统等设备，此处记录设备名称，否则为空                 |
| instance          | 数据采集节点名称，例如 `aiops-k8s-01`、`aiops-k8s-03`        |
| kpi_key           | 指标编码，例如 `pod_cpu_usage`                               |
| kpi_name          | 指标名称，例如 `CPU 使用率`                                  |
| kubernetes_node   | 保留字段，后续可标记 Kubernetes Node 名称       |
| mountpoint        | 若指标涉及文件系统等设备，此处记录文件系统挂载点路径，否则为空            |
| namespace         | Kubernetes 命名空间，例如 `hipstershop`                      |
| object_type       | 对象类型，例如 `pod`、`node`、`tidb`                         |
| pod               | Pod 名称，例如 `emailservice-2`、`productcatalogservice-2`     |
| pod_cpu_usage     | 指标值（根据 kpi_key 变化，例如 CPU 使用率时就是该字段为百分比；若 kpi_key 为其他，则由具体列命名）             |
| sql_type          | 保留字段，后续可标记 SQL 类型                           |
| type              | 保留字段，后续可标记数据行类型                   |


>infra 样例数据（部分）
>| time                  | cf   | device | instance       | kpi_key         | kpi_name  | kubernetes_node | mountpoint | namespace    | object_type | pod                          | pod_cpu_usage | sql_type | type |
>| --------------------- | ---- | ------ | -------------- | ---------------- | --------- | --------------- | ---------- | ------------ | ----------- | ---------------------------- | ------------- | -------- | ---- |
>| 2025-05-05 16:04:00+00:00  | null | null   | aiops-k8s-01   | pod_cpu_usage    | CPU使用率 | null            | null       | hipstershop  | pod         | emailservice-2               | 0.0           | null     | null |
>| 2025-05-05 16:04:00+00:00  | null | null   | aiops-k8s-01   | pod_cpu_usage    | CPU使用率 | null            | null       | hipstershop  | pod         | productcatalogservice-2       | 0.0           | null     | null |
>| 2025-05-05 16:04:00+00:00  | null | null   | aiops-k8s-01   | pod_cpu_usage    | CPU使用率 | null            | null       | hipstershop  | pod         | recommendationservice-1       | 0.0           | null     | null |
>| 2025-05-05 16:04:00+00:00  | null | null   | aiops-k8s-01   | pod_cpu_usage    | CPU使用率 | null            | null       | hipstershop  | pod         | shippingservice-2             | 0.0           | null     | null |
>| 2025-05-05 16:04:00+00:00  | null | null   | aiops-k8s-03   | pod_cpu_usage    | CPU使用率 | null            | null       | hipstershop  | pod         | adservice-2                   | 0.0           | null     | null |

在实际分析中，可根据不同 kpi_key 将 Parquet 文件加载为 DataFrame，结合 time 字段按时间序列绘制曲线图，观察资源使用趋势，并与业务指标跨表关联，评估应用性能与底层资源负载的关系。

#### 补充说明：DeepFlow 应用性能指标详细信息

根据 DeepFlow 官方文档，应用性能指标具有以下特点：

1. **自动生成**：无需向应用中插入任何代码，DeepFlow 自动生成所有服务的应用性能指标
2. **数据库表名**：
   - 服务列表：`flow_metrics.application`
   - 服务全景图：`flow_metrics.application_map`

3. **关键指标补充说明**：
   - `direction_score`：方向得分，算法推理应用层连接方向（客户端、服务端角色）的准确性得分值，得分越高连接方向的准确性越高，得分最高 255
   - `row`：行数，用于统计数据行数

4. **采集位置相关字段**：
   - `observation_point`：观测点，采集位置在流量路径中所处的逻辑位置，例如客户端网卡、客户端容器节点、服务端容器节点、服务端网卡等
   - `capture_network_type`：网络位置，采集流量的网络位置，使用固定值（云网络）表示云内流量，其他值表示传统 IDC 流量
   - `capture_nic`：采集网卡标识，当采集位置类型为本地网卡时，此值表示采集网卡的 MAC 地址后缀（后四字节）

5. **网络相关标签**：
   - `is_internet`：Internet IP 标志，IP 地址是否为外部 Internet 地址
   - `tunnel_type`：隧道类型
   - `nat_source`：NAT 源

### Trace

Trace 数据描述了微服务调用过程中各个 Span（子调用）的详细信息，帮助定位跨服务请求链路中的瓶颈与异常。主要字段与含义如下：

| 字段名             | 含义                                               | 单位 |
| ------------------ | -------------------------------------------------- | ---- |
| traceID            | Trace 唯一标识，用于将同一次请求在不同服务间关联。                                      | -    |
| spanID             | Span 唯一标识，用于表示该调用链中某个具体子调用。                                      | -    |
| flags              | Trace flags 值，通常表示采样与上下文传递信息。                                  | -    |
| operationName      | 操作名称，通常为微服务中某个 gRPC 或 HTTP 接口全路径，如 hipstershop.CartService/GetCart。                                            | -    |
| references         | 引用关系列表（如 CHILD_OF），用于表示该 Span 在调用链中的父子关系（例如当前 Span 是哪个上游 Span 的子调用）。                      | -    |
| startTime          | Span 开始时间，纳秒级时间戳（通常是 epoch 纳秒）。                            | 纳秒 |
| startTimeMillis    | Span 开始时间，毫秒级时间戳（对齐人类可读时间）。                            | 毫秒 |
| duration           | Span 持续时长，微秒级。     | 微秒 |
| tags               | 标签列表，包含一系列键值对，例如 RPC 系统（rpc.system）、Span 类型（span.kind）、gRPC 状态码（rpc.grpc.status_code）等。                              | -    |
| logs               | 日志列表，表示在 Span 执行过程中记录的事件，如 ServerRecv、ServerSend 等。                      | -    |
| process            | 进程信息，包含 serviceName（当前 Span 所属的服务名称）及一些附加标签（如 hostname）。             | -    |


> 样例数据（Trace 数据）
>| traceID                             | spanID                             | flags | operationName                                            | references                                                                                                    | startTime    | startTimeMillis | duration | tags                                                                                                                                                                                                                                                 | logs                                                                                                                                                                                                                                    | process                                                                                                                         |
>| ----------------------------------- | ---------------------------------- | ----- | -------------------------------------------------------- | ------------------------------------------------------------------------------------------------------------- | ------------ | --------------- | -------- | ---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------- | --------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------- | ------------------------------------------------------------------------------------------------------------------------------- |
>| 063346d9fb108c5fd56ecdeb9aae4e97    | a5dbaca343f5bf6b                   | 1.0   | hipstershop.CurrencyService/GetSupportedCurrencies       | [{'refType': 'CHILD_OF', 'spanID': '0473d09282f6f37b'}]                                                       | 1746028800342964 | 1746028800342  | 4202     | [{'key': 'rpc.system', 'type': 'string', 'value': 'grpc'}, {'key': 'span.kind', 'type': 'string', 'value': 'server'}, {'key': 'rpc.grpc.status_code', 'type': 'int64', 'value': 0}]                                                               | [{'fields': [{'key': 'message.type', 'type': 'string', 'value': 'EVENT'}, {'key': 'message.event', 'type': 'string', 'value': 'ServerRecv'}], 'timestamp': 1746028800343000}]                                                           | {'serviceName': 'frontend', 'tags': [{'key': 'hostname', 'type': 'string', 'value': 'frontend-xyz'}]}                           |
>| 44d06fcdceb3be247b1665f7affc4507    | c451558641c213e0                   | 1.0   | hipstershop.CartService/GetCart                           | [{'refType': 'CHILD_OF', 'spanID': '8e66f5b2da1c2e8f'}]                                                       | 1746028800375529 | 1746028800375  | 7034     | [{'key': 'rpc.system', 'type': 'string', 'value': 'grpc'}, {'key': 'span.kind', 'type': 'string', 'value': 'server'}, {'key': 'rpc.grpc.status_code', 'type': 'int64', 'value': 0}]                                                              | [{'fields': [{'key': 'message.type', 'type': 'string', 'value': 'EVENT'}, {'key': 'message.event', 'type': 'string', 'value': 'ServerRecv'}], 'timestamp': 1746028800376000}]                                                           | {'serviceName': 'checkoutservice', 'tags': [{'key': 'hostname', 'type': 'string', 'value': 'checkout-abc'}]}                    |


关联与使用

- 通过 `traceID` 可将同一次请求在多个微服务间的所有 Span 串联起来，绘制调用链图。
- `references` 字段中每个元素指明当前 Span 的父调用（CHILD_OF）。
- `startTimeMillis` 与 `duration` 可用于计算服务端响应时长和调用延迟。
- `tags` 中的指标（如 gRPC 状态码）有助于快速定位错误调用。
- `process` 包含采集时配置的服务名等信息，与 APM 数据中 object_id或日志中的 k8_pod 配合，可实现跨表联动，深入分析调用链根源。
  

### Log

Log 数据由 Filebeat 代理从容器中读取并推送至存储后，供后续文本检索与日志分析。字段说明如下：

| 字段名         | 含义                             |
| -------------- | -------------------------------- |
| k8_namespace   | Kubernetes 命名空间              |
| @timestamp     | 日志时间戳（ISO8601 格式，UTC时区）       |
| agent_name     | Filebeat 采集代理名称            |
| k8_pod         | Pod 名称                         |
| message        | 日志消息内容                     |
| k8_node_name   | Kubernetes Node 名称             |



> 样例数据（Log 数据）
>| k8_namespace | @timestamp               | agent_name                | k8_pod                 | message                                             | k8_node_name |
>| ------------ | ------------------------ | ------------------------- | ---------------------- | --------------------------------------------------- | ------------ |
>| hipstershop  | 2025-05-27T00:00:00Z | filebeat-filebeat-bdkxq   | cartservice-2          | Executed endpoint 'gRPC - /hipstershop.C...          | aiops-k8s-03 |
>| hipstershop  | 2025-05-27T00:00:00Z | filebeat-filebeat-bdkxq   | cartservice-2          | Executed endpoint 'gRPC - /hipstershop.C...          | aiops-k8s-03 |
>| hipstershop  | 2025-05-27T00:00:00Z | filebeat-filebeat-bdkxq   | recommendationservice-0 | {"timestamp": 1748275229.6932063, "severity": ...}   | aiops-k8s-03 |
>| hipstershop  | 2025-05-27T00:00:00Z | filebeat-filebeat-bdkxq   | cartservice-2          | Request finished HTTP/2 POST http://cart...          | aiops-k8s-03 |
>| hipstershop  | 2025-05-27T00:00:00Z | filebeat-filebeat-bdkxq   | frontend-0             | {"http.req.id":"9e697136-031f-40c4-abd5-6bccd5..."}  | aiops-k8s-03 |


## 注意事项

- **时区信息**

  所有文件名上的时间为 CST 时区，指标文件的`time`字段，日志文件的`@timestamp`字段为 UTC 时区。

- **字段命名不一致需注意对齐**

  例如 APM 表中使用 `time`、`object_id`；Infra 表中使用 `time`、`pod`（或 `instance` 代表 Node 名称）。跨表关联时，需要对齐字段名称与含义，便于合并分析。

- **保留字段（`cf`，`device`，`kubernetes_node`，`mountpoint`等）**

  当前样例数据中为 null 或空值，部分指标用这些字段标记设备信息、节点名称、挂载点等。

- **单位统一性**

  - 时延相关指标统一使用微秒（μs）作为单位
  - 字节相关指标统一使用字节（Bytes）作为单位
  - 比例相关指标统一使用百分比（%）作为单位
  - 计数相关指标统一使用个数作为单位

- **数据精度**

  - 时间戳字段：纳秒级精度（startTime）或毫秒级精度（startTimeMillis）
  - 时延字段：微秒级精度
  - 比例字段：保留小数点后两位

- **异常状态码定义**

  根据具体应用协议的响应码判断异常，不同协议的定义详见 `l7_flow_log` 中 `response_status` 字段的说明。常见协议异常定义：
  - HTTP：4xx 为客户端异常，5xx 为服务端异常
  - gRPC：非 0 状态码为异常
  - 超时定义：TCP 类应用在 1800s 内未采集到响应，UDP 类应用在 150s 内未采集到响应

## LICENSE

Unless otherwise agreed by the organizers and the contestant, the contestant shall ensure that it only uses the basic data for non-commercial purposes such as scientific research or classroom teaching, and take full responsibility for the use of conversion basic data, also ensure the organizer and its affiliated party are free from expenses or litigation caused by the any use of basic data.
