#!/usr/bin/env python3
"""
Python版本检查脚本
确保Python版本满足AIOps系统要求 (>= 3.12)
"""

import sys
import platform

def check_python_version():
    """检查Python版本是否满足要求"""
    print("🐍 Python版本检查工具")
    print("=" * 50)
    
    # 获取版本信息
    version_info = sys.version_info
    version_str = f"{version_info.major}.{version_info.minor}.{version_info.micro}"
    
    print(f"📋 系统信息:")
    print(f"  Python版本: {version_str}")
    print(f"  Python路径: {sys.executable}")
    print(f"  操作系统: {platform.system()} {platform.release()}")
    print(f"  架构: {platform.machine()}")
    
    # 检查版本要求
    required_major = 3
    required_minor = 12
    
    print(f"\n🔍 版本要求检查:")
    print(f"  要求版本: >= {required_major}.{required_minor}.0")
    print(f"  当前版本: {version_str}")
    
    if version_info.major < required_major:
        print(f"❌ Python主版本不满足要求 (需要Python 3)")
        return False
    elif version_info.major == required_major and version_info.minor < required_minor:
        print(f"❌ Python次版本不满足要求 (需要3.{required_minor}+)")
        print(f"\n🔧 升级建议:")
        show_upgrade_suggestions()
        return False
    else:
        print(f"✅ Python版本满足要求!")
        return True

def show_upgrade_suggestions():
    """显示Python升级建议"""
    system = platform.system().lower()
    
    print(f"根据您的操作系统 ({platform.system()})，建议使用以下方法升级Python:")
    print()
    
    if "linux" in system:
        print("🐧 Linux系统:")
        print("  Ubuntu/Debian:")
        print("    sudo apt update")
        print("    sudo apt install python3.12 python3.12-venv python3.12-pip")
        print("    sudo update-alternatives --install /usr/bin/python3 python3 /usr/bin/python3.12 1")
        print()
        print("  CentOS/RHEL/Fedora:")
        print("    sudo dnf install python3.12 python3.12-pip")
        print("    # 或者")
        print("    sudo yum install python3.12 python3.12-pip")
        print()
        
    elif "darwin" in system:
        print("🍎 macOS系统:")
        print("  使用Homebrew (推荐):")
        print("    brew install python@3.12")
        print("    brew link python@3.12")
        print()
        print("  使用官方安装包:")
        print("    访问 https://www.python.org/downloads/")
        print()
        
    elif "windows" in system:
        print("🪟 Windows系统:")
        print("  官方安装包 (推荐):")
        print("    访问 https://www.python.org/downloads/")
        print("    下载Python 3.12.x安装包")
        print()
        print("  使用Chocolatey:")
        print("    choco install python312")
        print()
        print("  使用winget:")
        print("    winget install Python.Python.3.12")
        print()
    
    print("🔧 通用方法 - 使用pyenv (推荐):")
    print("  安装pyenv:")
    if "windows" in system:
        print("    git clone https://github.com/pyenv-win/pyenv-win.git %USERPROFILE%\\.pyenv")
    else:
        print("    curl https://pyenv.run | bash")
    print("  安装Python 3.12:")
    print("    pyenv install 3.12.0")
    print("    pyenv global 3.12.0")
    print()
    
    print("📝 验证安装:")
    print("  python3 --version")
    print("  python3 -c \"import sys; print(f'Python {sys.version_info.major}.{sys.version_info.minor}.{sys.version_info.micro}')\"")

def main():
    """主函数"""
    try:
        success = check_python_version()
        
        if success:
            print(f"\n🎉 恭喜！您的Python版本满足AIOps系统要求")
            print(f"📝 下一步:")
            print(f"  1. 创建虚拟环境: python3 -m venv venv")
            print(f"  2. 激活虚拟环境: source venv/bin/activate")
            print(f"  3. 安装依赖: pip install -r requirements.txt")
            print(f"  4. 检查依赖: python check_dependencies.py")
            return True
        else:
            print(f"\n❌ Python版本不满足要求，请升级后重试")
            return False
            
    except Exception as e:
        print(f"❌ 检查过程中出现错误: {e}")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
