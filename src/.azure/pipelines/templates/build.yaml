parameters:
  - name: build_configuration
    displayName: Build configuration
    type: string
    default: Release
    values:
    - Release
    - Debug
  - name: include_suffix
    displayName: Append version suffix
    type: boolean
    default: true
  - name: version_suffix
    displayName: Version suffix
    type: string
    default: ci.$(Build.BuildNumber)
  - name: codesign
    displayName: Enable code signing
    type: boolean
    default: false
  - name: skip_test
    displayName: Skip tests
    type: boolean
    default: false
  - name: publish_nightly
    displayName: Publish to autogen-nightly
    type: boolean
    default: false
  - name: publish_nuget
    displayName: Publish to nuget.org
    type: boolean
    default: false
  - name: publish_artifacts
    displayName: Publish artifacts
    type: boolean
    default: false
  - name: runCodeQL3000
    default: false
    displayName: Run CodeQL3000 tasks
    type: boolean

jobs:

# Build, sign dlls, build nuget pkgs, then sign them
- job: Build
  displayName: Build and create NuGet packages
  variables:
    publishVstsFeed: 'AGPublic/AutoGen-Nightly'
    ${{ if eq(parameters.codesign, true) }}:
      esrp_signing: true
    ${{ else }}:
      esrp_signing: false
  ${{ if ne(variables['System.TeamProject'], 'GitHub - PR Builds') }}:
    templateContext:
      outputs:
      # Publish artifacts if enabled
      - ${{ if eq(parameters.publish_artifacts, true) }}: # TODO add eq(parameters.codesign, true)
        - output: pipelineArtifact
          targetPath: '$(build.sourcesdirectory)/dotnet/artifacts'
          artifactName: artifacts folder
      # Publish packages to nightly
      - ${{ if eq(parameters.publish_nightly, true) }}: # TODO add eq(parameters.codesign, true)
        - output: nuget
          useDotNetTask: false
          packageParentPath: $(Pipeline.Workspace)
          packagesToPush: $(build.sourcesdirectory)/dotnet/artifacts/**/*.nupkg;$(build.sourcesdirectory)/dotnet/artifacts/**/*.snupkg
          nuGetFeedType: internal
          publishVstsFeed: $(publishVstsFeed)
          allowPackageConflicts: true
      - ${{ if and(eq(parameters.codesign, true), eq(parameters.publish_nuget, true)) }}:
        - output: nuget
          condition: succeeded()
          useDotNetTask: false
          packageParentPath: $(Pipeline.Workspace)
          packagesToPush: $(build.sourcesdirectory)/dotnet/artifacts/**/*.nupkg;$(build.sourcesdirectory)/dotnet/artifacts/**/*.snupkg
          nuGetFeedType: external
          publishFeedCredentials: dotnet-orleans-nuget
          publishPackageMetadata: true
          allowPackageConflicts: true
  steps:
  - checkout: self
  - task: UseDotNet@2
    displayName: 'Use .NET Core sdk'
    inputs:
      useGlobalJson: true
      workingDirectory: $(Build.SourcesDirectory)/dotnet
  - task: PowerShell@2
    displayName: 'Install uv'
    inputs:
      targetType: 'inline'
      script: |
        irm https://astral.sh/uv/install.ps1 | iex
        $env:Path = "C:\Users\<USER>\.local\bin;$env:Path"
        uv --version
  - task: Bash@3
    displayName: Install .NET Aspire workload
    inputs:
      targetType: 'inline'
      script: |
        dotnet nuget locals all --clear
        dotnet workload install aspire
  - ${{ if eq(variables.runCodeQL3000, 'true') }}:
    - task: CodeQL3000Init@0
      displayName: CodeQL Initialize
    # This task only tags a build if it actually does CodeQL3000 work.
    # Those tasks no-op while the analysis is considered up to date i.e. for runs w/in a few days of each other.
    - script: "echo ##vso[build.addbuildtag]CodeQL3000"
      displayName: 'Set CI CodeQL3000 tag'
      condition: ne(variables.CODEQL_DIST,'')
  - task: DotNetCoreCLI@2
    displayName: Build
    inputs:
      command: build
      arguments: '$(build_flags) /bl:${{parameters.build_configuration}}-Build.binlog /p:Configuration=${{parameters.build_configuration}} $(solution)'
      workingDirectory: $(Build.SourcesDirectory)/dotnet
    env:
      PATH: "C:\\Users\\<USER>\\.local\\bin;$(PATH)"
      ${{ if and(eq(parameters.include_suffix, true), eq(parameters.publish_nuget, false)) }}:
        VersionSuffix: ${{parameters.version_suffix}}
      OfficialBuild: $(official_build)

  - ${{ if eq(variables.runCodeQL3000, 'true') }}:
    - task: CodeQL3000Finalize@0
      displayName: CodeQL Finalize
  # DLL code signing
  - ${{ if eq(variables.esrp_signing, true) }}:
    - task: UseDotNet@2
      displayName: 'Codesign: Use .NET Core'
      inputs:
        packageType: runtime
        version: $(codesign_runtime)
    - task: CopyFiles@2
      displayName: 'Codesign: Copy Files for signing'
      inputs:
        SourceFolder: '$(build.sourcesdirectory)'
        Contents: |
          **/bin/**/AutoGen*.dll
          **/bin/**/Microsoft.AutoGen.*.dll
        TargetFolder: '$(build.artifactstagingdirectory)\codesign'
        CleanTargetFolder: true
    - task: SFP.build-tasks.custom-build-task-1.EsrpCodeSigning@5
      displayName: 'Codesign: ESRP CodeSigning (dlls)'
      inputs:
        ConnectedServiceName: 'AI Frontiers ESRP'
        AppRegistrationClientId: 'c1e7a5c0-ee6b-4cec-9e11-4dc3f4670042'
        AppRegistrationTenantId: '975f013f-7f24-47e8-a7d3-abc4752bf346'
        AuthAKVName: 'aif-autogen-esrp-kv'
        AuthCertName: 'AIF-PME-InfrastructureAuth'
        AuthSignCertName: 'AutoGenPublishESRPPKI' # this variable is only needed for codesign
        FolderPath: '$(build.artifactstagingdirectory)\codesign'
        Pattern: '*.dll'
        signConfigType: inlineSignParams
        inlineOperation: |
          [
            {
              "keyCode": "CP-230012",
              "operationSetCode": "SigntoolSign",
              "parameters": [
                {
                  "parameterName": "OpusName",
                  "parameterValue": "Microsoft"
                },
                {
                  "parameterName": "OpusInfo",
                  "parameterValue": "http://www.microsoft.com"
                },
                {
                  "parameterName": "FileDigest",
                  "parameterValue": "/fd \"SHA256\""
                },
                {
                  "parameterName": "PageHash",
                  "parameterValue": "/NPH"
                },
                {
                  "parameterName": "TimeStamp",
                  "parameterValue": "/tr \"http://rfc3161.gtm.corp.microsoft.com/TSS/HttpTspServer\" /td sha256"
                }
              ],
              "toolName": "sign",
              "toolVersion": "1.0"
            },
            {
              "keyCode": "CP-230012",
              "operationSetCode": "SigntoolVerify",
              "parameters": [ ],
              "toolName": "sign",
              "toolVersion": "1.0"
            }
          ]
        SessionTimeout: 180
        VerboseLogin: true
    - task: CopyFiles@2
      displayName: 'Codesign: Copy Signed Files Back'
      inputs:
        SourceFolder: '$(build.artifactstagingdirectory)\codesign'
        Contents: '**\*'
        TargetFolder: '$(build.sourcesdirectory)'
        OverWrite: true
    # End DLL code signing
  - task: CmdLine@2
    displayName: Pack
    inputs:
      script: 'dotnet pack --no-build --no-restore $(build_flags) /bl:${{parameters.build_configuration}}-Pack.binlog /p:Configuration=${{parameters.build_configuration}} $(solution)'
      workingDirectory: $(Build.SourcesDirectory)/dotnet
    env:
      ${{ if and(eq(parameters.include_suffix, true), eq(parameters.publish_nuget, false)) }}:
        VersionSuffix: ${{parameters.version_suffix}}
      OfficialBuild: $(official_build)
  # NuGet code signing
  - ${{ if eq(variables.esrp_signing, true) }}:
    - task: UseDotNet@2
      displayName: 'Codesign: Use .NET Core'
      inputs:
        packageType: runtime
        version: $(codesign_runtime)
    - task: SFP.build-tasks.custom-build-task-1.EsrpCodeSigning@5
      displayName: 'Codesign: ESRP CodeSigning (nuget)'
      inputs:
        ConnectedServiceName: 'AI Frontiers ESRP'
        AppRegistrationClientId: 'c1e7a5c0-ee6b-4cec-9e11-4dc3f4670042'
        AppRegistrationTenantId: '975f013f-7f24-47e8-a7d3-abc4752bf346'
        AuthAKVName: 'aif-autogen-esrp-kv'
        AuthCertName: 'AIF-PME-InfrastructureAuth'
        AuthSignCertName: 'AutoGenPublishESRPPKI' # this variable is only needed for codesign
        FolderPath: '$(build.sourcesdirectory)/dotnet/artifacts/package/${{parameters.build_configuration}}'
        Pattern: '*.nupkg'
        signConfigType: inlineSignParams
        inlineOperation: |
          [
            {
              "keyCode": "CP-401405",
              "operationSetCode": "NuGetSign",
              "parameters": [],
              "toolName": "sign",
              "toolVersion": "1.0"
            },
            {
              "keyCode": "CP-401405",
              "operationSetCode": "NuGetVerify",
              "parameters": [ ],
              "toolName": "sign",
              "toolVersion": "1.0"
            }
          ]
        SessionTimeout: 180
        VerboseLogin: true