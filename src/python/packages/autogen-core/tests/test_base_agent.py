import pytest
from autogen_core import <PERSON><PERSON><PERSON>, AgentInstantiationContext, AgentRunt<PERSON>
from autogen_test_utils import Noop<PERSON><PERSON>
from pytest_mock import Mocker<PERSON><PERSON>ture


@pytest.mark.asyncio
async def test_base_agent_create(mocker: MockerFixture) -> None:
    runtime = mocker.Mock(spec=AgentRuntime)

    # Shows how to set the context for the agent instantiation in a test context
    with AgentInstantiationContext.populate_context((runtime, AgentId("name", "namespace"))):
        agent = NoopAgent()
        assert agent.runtime == runtime
        assert agent.id == AgentId("name", "namespace")
