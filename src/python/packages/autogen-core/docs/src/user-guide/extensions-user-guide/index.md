---
myst:
  html_meta:
    "description lang=en": |
      User Guide for AutoGen Extensions, a framework for building multi-agent applications with AI agents.
---

# Extensions

```{toctree}
:maxdepth: 3
:hidden:

installation
discover
create-your-own
```

```{toctree}
:maxdepth: 3
:hidden:
:caption: Guides

azure-container-code-executor
```

AutoGen is designed to be extensible. The `autogen-ext` package contains many different component implementations maintained by the AutoGen project. However, we strongly encourage others to build their own components and publish them as part of the ecosytem.


::::{grid} 2 2 2 2
:gutter: 3

:::{grid-item-card} {fas}`magnifying-glass;pst-color-primary` Discover
:link: ./discover.html

Discover community extensions and samples
:::

:::{grid-item-card} {fas}`code;pst-color-primary` Create your own
:link: ./create-your-own.html

Create your own extension
:::
::::
