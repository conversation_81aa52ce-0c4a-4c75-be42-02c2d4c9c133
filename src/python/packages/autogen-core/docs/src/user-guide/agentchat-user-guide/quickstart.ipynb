{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# Quickstart"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Via AgentChat, you can build applications quickly using preset agents.\n", "To illustrate this, we will begin with creating a team of a single tool-use\n", "agent that you can chat with.\n", "\n", "The following code uses the OpenAI model. If you haven't already, you need to\n", "install the following package and extension:"]}, {"cell_type": "code", "execution_count": null, "metadata": {"vscode": {"languageId": "shellscript"}}, "outputs": [], "source": ["pip install -U \"autogen-agentchat\" \"autogen-ext[openai,azure]\""]}, {"cell_type": "markdown", "metadata": {}, "source": ["To use Azure OpenAI models and AAD authentication,\n", "you can follow the instructions [here](./tutorial/models.ipynb#azure-openai)."]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["---------- user ----------\n", "What is the weather in NYC?\n", "---------- weather_agent ----------\n", "[FunctionCall(id='call_vN04UiNJgqSz6g3MHt7Renig', arguments='{\"city\":\"New York City\"}', name='get_weather')]\n", "[Prompt tokens: 75, Completion tokens: 16]\n", "---------- weather_agent ----------\n", "[FunctionExecutionResult(content='The weather in New York City is 73 degrees and Sunny.', call_id='call_vN04UiNJgqSz6g3MHt7Renig')]\n", "---------- weather_agent ----------\n", "The weather in New York City is 73 degrees and Sunny.\n", "---------- Summary ----------\n", "Number of messages: 4\n", "Finish reason: Maximum number of turns 1 reached.\n", "Total prompt tokens: 75\n", "Total completion tokens: 16\n", "Duration: 1.15 seconds\n", "---------- user ----------\n", "What is the weather in Seattle?\n", "---------- weather_agent ----------\n", "[FunctionCall(id='call_BesYutZXJIMfu2TlDZgodIEj', arguments='{\"city\":\"Seattle\"}', name='get_weather')]\n", "[Prompt tokens: 127, Completion tokens: 14]\n", "---------- weather_agent ----------\n", "[FunctionExecutionResult(content='The weather in Seattle is 73 degrees and Sunny.', call_id='call_BesYutZXJIMfu2TlDZgodIEj')]\n", "---------- weather_agent ----------\n", "The weather in Seattle is 73 degrees and Sunny.\n", "---------- Summary ----------\n", "Number of messages: 4\n", "Finish reason: Maximum number of turns 1 reached.\n", "Total prompt tokens: 127\n", "Total completion tokens: 14\n", "Duration: 2.38 seconds\n"]}], "source": ["from autogen_agentchat.agents import AssistantAgent\n", "from autogen_agentchat.teams import RoundRobinGroupChat\n", "from autogen_agentchat.ui import Console\n", "from autogen_ext.models.openai import OpenAIChatCompletionClient\n", "\n", "\n", "# Define a tool\n", "async def get_weather(city: str) -> str:\n", "    return f\"The weather in {city} is 73 degrees and Sunny.\"\n", "\n", "\n", "async def main() -> None:\n", "    # Define an agent\n", "    weather_agent = AssistantAgent(\n", "        name=\"weather_agent\",\n", "        model_client=OpenAIChatCompletionClient(\n", "            model=\"gpt-4o-2024-08-06\",\n", "            # api_key=\"YOUR_API_KEY\",\n", "        ),\n", "        tools=[get_weather],\n", "    )\n", "\n", "    # Define a team with a single agent and maximum auto-gen turns of 1.\n", "    agent_team = RoundRobinGroupChat([weather_agent], max_turns=1)\n", "\n", "    while True:\n", "        # Get user input from the console.\n", "        user_input = input(\"Enter a message (type 'exit' to leave): \")\n", "        if user_input.strip().lower() == \"exit\":\n", "            break\n", "        # Run the team and stream messages to the console.\n", "        stream = agent_team.run_stream(task=user_input)\n", "        await <PERSON><PERSON><PERSON>(stream)\n", "\n", "\n", "# NOTE: if running this inside a Python script you'll need to use asyncio.run(main()).\n", "await main()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["The code snippet above introduces two high level concepts in AgentChat: *Agent* and *Team*.  An Agent helps us define what actions are taken when a message is received. Specifically, we use the {py:class}`~autogen_agentchat.agents.AssistantAgent` preset - an agent that can be given access to a model (e.g., LLM) and tools (functions) that it can then use to address tasks.  A Team helps us define the rules for how agents interact with each other.  In the  {py:class}`~autogen_agentchat.teams.RoundRobinGroupChat` team, agents respond in a sequential round-robin fashion.\n", "In this case, we have a single agent, so the same agent is used for each round."]}, {"cell_type": "markdown", "metadata": {}, "source": ["## What's Next?\n", "\n", "Now that you have a basic understanding of how to define an agent and a team, consider following the [tutorial](./tutorial/models) for a walkthrough on other features of AgentChat.\n", "\n"]}], "metadata": {"kernelspec": {"display_name": ".venv", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.5"}}, "nbformat": 4, "nbformat_minor": 2}