{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# Swarm\n", "\n", "{py:class}`~autogen_agentchat.teams.Swarm` implements a team in which agents can hand off \n", "task to other agents based on their capabilities. \n", "It is a multi-agent design pattern first introduced by OpenAI in \n", "[Swarm](https://github.com/openai/swarm).\n", "The key idea is to let agent delegate tasks to other agents using a special tool call, while\n", "all agents share the same message context.\n", "This enables agents to make local decisions about task planning, rather than\n", "relying on a central orchestrator such as in {py:class}`~autogen_agentchat.teams.SelectorGroupChat`.\n", "\n", "```{note}\n", "{py:class}`~autogen_agentchat.teams.Swarm` is a high-level API. If you need more\n", "control and customization that is not supported by this API, you can take a look\n", "at the [Handoff Pattern](../core-user-guide/design-patterns/handoffs.ipynb)\n", "in the Core API documentation and implement your own version of the Swarm pattern.\n", "```\n", "\n", "## How Does It Work?\n", "\n", "At its core, the {py:class}`~autogen_agentchat.teams.Swarm` team is a group chat\n", "where agents take turn to generate a response. \n", "Similar to {py:class}`~autogen_agentchat.teams.SelectorGroupChat`\n", "and {py:class}`~autogen_agentchat.teams.RoundRobinGroupChat`, participant agents\n", "broadcast their responses so all agents share the same message context.\n", "\n", "Different from the other two group chat teams, at each turn,\n", "**the speaker agent is selected based on the most recent\n", "{py:class}`~autogen_agentchat.messages.HandoffMessage` message in the context.**\n", "This naturally requires each agent in the team to be able to generate\n", "{py:class}`~autogen_agentchat.messages.HandoffMessage` to signal\n", "which other agents that it hands off to.\n", "\n", "For {py:class}`~autogen_agentchat.agents.AssistantAgent`, you can set the\n", "`handoffs` argument to specify which agents it can hand off to. You can\n", "use {py:class}`~autogen_agentchat.base.Handoff` to customize the message\n", "content and handoff behavior.\n", "\n", "The overall process can be summarized as follows:\n", "\n", "1. Each agent has the ability to generate {py:class}`~autogen_agentchat.messages.HandoffMessage`\n", "   to signal which other agents it can hand off to. For {py:class}`~autogen_agentchat.agents.AssistantAgent`, this means setting the `handoffs` argument.\n", "2. When the team starts on a task, the first speaker agents operate on the task and make locallized decision about whether to hand off and to whom.\n", "3. When an agent generates a {py:class}`~autogen_agentchat.messages.HandoffMessage`, the receiving agent takes over the task with the same message context.\n", "4. The process continues until a termination condition is met.\n", "\n", "```{note}\n", "The {py:class}`~autogen_agentchat.agents.AssistantAgent` uses the tool calling\n", "capability of the model to generate handoffs. This means that the model must\n", "support tool calling. If the model does parallel tool calling, multiple handoffs\n", "may be generated at the same time. This can lead to unexpected behavior.\n", "To avoid this, you can disable parallel tool calling by configuring the model\n", "client. For {py:class}`~autogen_ext.models.openai.OpenAIChatCompletionClient`\n", "and {py:class}`~autogen_ext.models.openai.AzureOpenAIChatCompletionClient`,\n", "you can set `parallel_tool_calls=False` in the configuration.\n", "```\n", "\n", "In this section, we will show you two examples of how to use the {py:class}`~autogen_agentchat.teams.Swarm` team:\n", "\n", "1. A customer support team with human-in-the-loop handoff.\n", "2. An automonous team for content generation."]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Customer Support Example"]}, {"cell_type": "markdown", "metadata": {}, "source": ["![Customer Support](swarm_customer_support.svg)\n", "\n", "This system implements a flights refund scenario with two agents:\n", "\n", "- **Travel Agent**: Handles general travel and refund coordination.\n", "- **Flights Refunder**: Specializes in processing flight refunds with the `refund_flight` tool.\n", "\n", "Additionally, we let the user interact with the agents, when agents handoff to `\"user\"`.\n", "\n", "#### Workflow\n", "1. The **Travel Agent** initiates the conversation and evaluates the user's request.\n", "2. Based on the request:\n", "   - For refund-related tasks, the Travel Agent hands off to the **Flights Refunder**.\n", "   - For information needed from the customer, either agent can hand off to the `\"user\"`.\n", "3. The **Flights Refunder** processes refunds using the `refund_flight` tool when appropriate.\n", "4. If an agent hands off to the `\"user\"`, the team execution will stop and wait for the user to input a response.\n", "5. When the user provides input, it's sent back to the team as a {py:class}`~autogen_agentchat.messages.HandoffMessage`. This message is directed to the agent that originally requested user input.\n", "6. The process continues until the Travel Agent determines the task is complete and terminates the workflow."]}, {"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [], "source": ["from typing import Any, Dict, List\n", "\n", "from autogen_agentchat.agents import AssistantAgent\n", "from autogen_agentchat.conditions import HandoffTermination, TextMentionTermination\n", "from autogen_agentchat.messages import HandoffMessage\n", "from autogen_agentchat.teams import Swarm\n", "from autogen_agentchat.ui import Console\n", "from autogen_ext.models.openai import OpenAIChatCompletionClient"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Tools"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [], "source": ["def refund_flight(flight_id: str) -> str:\n", "    \"\"\"Refund a flight\"\"\"\n", "    return f\"Flight {flight_id} refunded\""]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Agents"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [], "source": ["model_client = OpenAIChatCompletionClient(\n", "    model=\"gpt-4o\",\n", "    # api_key=\"YOUR_API_KEY\",\n", ")\n", "\n", "travel_agent = AssistantAgent(\n", "    \"travel_agent\",\n", "    model_client=model_client,\n", "    handoffs=[\"flights_refunder\", \"user\"],\n", "    system_message=\"\"\"You are a travel agent.\n", "    The flights_refunder is in charge of refunding flights.\n", "    If you need information from the user, you must first send your message, then you can handoff to the user.\n", "    Use TERMINATE when the travel planning is complete.\"\"\",\n", ")\n", "\n", "flights_refunder = AssistantAgent(\n", "    \"flights_refunder\",\n", "    model_client=model_client,\n", "    handoffs=[\"travel_agent\", \"user\"],\n", "    tools=[refund_flight],\n", "    system_message=\"\"\"You are an agent specialized in refunding flights.\n", "    You only need flight reference numbers to refund a flight.\n", "    You have the ability to refund a flight using the refund_flight tool.\n", "    If you need information from the user, you must first send your message, then you can handoff to the user.\n", "    When the transaction is complete, handoff to the travel agent to finalize.\"\"\",\n", ")"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [], "source": ["termination = HandoffTermination(target=\"user\") | TextMentionTermination(\"TERMINATE\")\n", "team = Swarm([travel_agent, flights_refunder], termination_condition=termination)"]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["---------- user ----------\n", "I need to refund my flight.\n", "---------- travel_agent ----------\n", "[FunctionCall(id='call_ZQ2rGjq4Z29pd0yP2sNcuyd2', arguments='{}', name='transfer_to_flights_refunder')]\n", "[Prompt tokens: 119, Completion tokens: 14]\n", "---------- travel_agent ----------\n", "[FunctionExecutionResult(content='Transferred to flights_refunder, adopting the role of flights_refunder immediately.', call_id='call_ZQ2rGjq4Z29pd0yP2sNcuyd2')]\n", "---------- travel_agent ----------\n", "Transferred to flights_refunder, adopting the role of flights_refunder immediately.\n", "---------- flights_refunder ----------\n", "Could you please provide me with the flight reference number so I can process the refund for you?\n", "[Prompt tokens: 191, Completion tokens: 20]\n", "---------- flights_refunder ----------\n", "[FunctionCall(id='call_1iRfzNpxTJhRTW2ww9aQJ8sK', arguments='{}', name='transfer_to_user')]\n", "[Prompt tokens: 219, Completion tokens: 11]\n", "---------- flights_refunder ----------\n", "[FunctionExecutionResult(content='Transferred to user, adopting the role of user immediately.', call_id='call_1iRfzNpxTJhRTW2ww9aQJ8sK')]\n", "---------- flights_refunder ----------\n", "Transferred to user, adopting the role of user immediately.\n", "---------- Summary ----------\n", "Number of messages: 8\n", "Finish reason: Handoff to user from flights_refunder detected.\n", "Total prompt tokens: 529\n", "Total completion tokens: 45\n", "Duration: 2.05 seconds\n", "---------- user ----------\n", "Sure, it's 507811\n", "---------- flights_refunder ----------\n", "[FunctionCall(id='call_UKCsoEBdflkvpuT9Bi2xlvTd', arguments='{\"flight_id\":\"507811\"}', name='refund_flight')]\n", "[Prompt tokens: 266, Completion tokens: 18]\n", "---------- flights_refunder ----------\n", "[FunctionExecutionResult(content='Flight 507811 refunded', call_id='call_UKCsoEBdflkvpuT9Bi2xlvTd')]\n", "---------- flights_refunder ----------\n", "Tool calls:\n", "refund_flight({\"flight_id\":\"507811\"}) = Flight 507811 refunded\n", "---------- flights_refunder ----------\n", "[FunctionCall(id='call_MQ2CXR8UhVtjNc6jG3wSQp2W', arguments='{}', name='transfer_to_travel_agent')]\n", "[Prompt tokens: 303, Completion tokens: 13]\n", "---------- flights_refunder ----------\n", "[FunctionExecutionResult(content='Transferred to travel_agent, adopting the role of travel_agent immediately.', call_id='call_MQ2CXR8UhVtjNc6jG3wSQp2W')]\n", "---------- flights_refunder ----------\n", "Transferred to travel_agent, adopting the role of travel_agent immediately.\n", "---------- travel_agent ----------\n", "Your flight with reference number 507811 has been successfully refunded. If you need anything else, feel free to let me know. Safe travels! TERMINATE\n", "[Prompt tokens: 272, Completion tokens: 32]\n", "---------- Summary ----------\n", "Number of messages: 8\n", "Finish reason: Text 'TERMINATE' mentioned\n", "Total prompt tokens: 841\n", "Total completion tokens: 63\n", "Duration: 1.64 seconds\n"]}], "source": ["task = \"I need to refund my flight.\"\n", "\n", "\n", "async def run_team_stream() -> None:\n", "    task_result = await <PERSON><PERSON>e(team.run_stream(task=task))\n", "    last_message = task_result.messages[-1]\n", "\n", "    while isinstance(last_message, HandoffMessage) and last_message.target == \"user\":\n", "        user_message = input(\"User: \")\n", "\n", "        task_result = await <PERSON><PERSON>e(\n", "            team.run_stream(task=HandoffMessage(source=\"user\", target=last_message.source, content=user_message))\n", "        )\n", "        last_message = task_result.messages[-1]\n", "\n", "\n", "# Use asyncio.run(...) if you are running this in a script.\n", "await run_team_stream()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Stock Research Example"]}, {"cell_type": "markdown", "metadata": {}, "source": ["![Stock Research](swarm_stock_research.svg)\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["This system is designed to perform stock research tasks by leveraging four agents:\n", "\n", "- **Planner**: The central coordinator that delegates specific tasks to specialized agents based on their expertise. The planner ensures that each agent is utilized efficiently and oversees the overall workflow.\n", "- **Financial Analyst**: A specialized agent responsible for analyzing financial metrics and stock data using tools such as `get_stock_data`.\n", "- **News Analyst**: An agent focused on gathering and summarizing recent news articles relevant to the stock, using tools such as `get_news`.\n", "- **Writer**: An agent tasked with compiling the findings from the stock and news analysis into a cohesive final report.\n", "\n", "#### Workflow\n", "1. The **Planner** initiates the research process by delegating tasks to the appropriate agents in a step-by-step manner.\n", "2. Each agent performs its task independently and appends their work to the shared **message thread/history**. Rather than directly returning results to the planner, all agents contribute to and read from this shared message history. When agents generate their work using the LLM, they have access to this shared message history, which provides context and helps track the overall progress of the task.\n", "3. Once an agent completes its task, it hands off control back to the planner.\n", "4. The process continues until the planner determines that all necessary tasks have been completed and decides to terminate the workflow."]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Tools"]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [], "source": ["async def get_stock_data(symbol: str) -> Dict[str, Any]:\n", "    \"\"\"Get stock market data for a given symbol\"\"\"\n", "    return {\"price\": 180.25, \"volume\": 1000000, \"pe_ratio\": 65.4, \"market_cap\": \"700B\"}\n", "\n", "\n", "async def get_news(query: str) -> List[Dict[str, str]]:\n", "    \"\"\"Get recent news articles about a company\"\"\"\n", "    return [\n", "        {\n", "            \"title\": \"Tesla Expands Cybertruck Production\",\n", "            \"date\": \"2024-03-20\",\n", "            \"summary\": \"Tesla ramps up Cybertruck manufacturing capacity at Gigafactory Texas, aiming to meet strong demand.\",\n", "        },\n", "        {\n", "            \"title\": \"Tesla FSD Beta Shows Promise\",\n", "            \"date\": \"2024-03-19\",\n", "            \"summary\": \"Latest Full Self-Driving beta demonstrates significant improvements in urban navigation and safety features.\",\n", "        },\n", "        {\n", "            \"title\": \"Model Y Dominates Global EV Sales\",\n", "            \"date\": \"2024-03-18\",\n", "            \"summary\": \"Tesla's Model Y becomes best-selling electric vehicle worldwide, capturing significant market share.\",\n", "        },\n", "    ]"]}, {"cell_type": "code", "execution_count": 11, "metadata": {}, "outputs": [], "source": ["model_client = OpenAIChatCompletionClient(\n", "    model=\"gpt-4o\",\n", "    # api_key=\"YOUR_API_KEY\",\n", ")\n", "\n", "planner = AssistantAgent(\n", "    \"planner\",\n", "    model_client=model_client,\n", "    handoffs=[\"financial_analyst\", \"news_analyst\", \"writer\"],\n", "    system_message=\"\"\"You are a research planning coordinator.\n", "    Coordinate market research by delegating to specialized agents:\n", "    - Financial Analyst: For stock data analysis\n", "    - News Analyst: For news gathering and analysis\n", "    - Writer: For compiling final report\n", "    Always send your plan first, then handoff to appropriate agent.\n", "    Always handoff to a single agent at a time.\n", "    Use TERMINATE when research is complete.\"\"\",\n", ")\n", "\n", "financial_analyst = AssistantAgent(\n", "    \"financial_analyst\",\n", "    model_client=model_client,\n", "    handoffs=[\"planner\"],\n", "    tools=[get_stock_data],\n", "    system_message=\"\"\"You are a financial analyst.\n", "    Analyze stock market data using the get_stock_data tool.\n", "    Provide insights on financial metrics.\n", "    Always handoff back to planner when analysis is complete.\"\"\",\n", ")\n", "\n", "news_analyst = AssistantAgent(\n", "    \"news_analyst\",\n", "    model_client=model_client,\n", "    handoffs=[\"planner\"],\n", "    tools=[get_news],\n", "    system_message=\"\"\"You are a news analyst.\n", "    Gather and analyze relevant news using the get_news tool.\n", "    Summarize key market insights from news.\n", "    Always handoff back to planner when analysis is complete.\"\"\",\n", ")\n", "\n", "writer = AssistantAgent(\n", "    \"writer\",\n", "    model_client=model_client,\n", "    handoffs=[\"planner\"],\n", "    system_message=\"\"\"You are a financial report writer.\n", "    Compile research findings into clear, concise reports.\n", "    Always handoff back to planner when writing is complete.\"\"\",\n", ")"]}, {"cell_type": "code", "execution_count": 12, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["---------- user ----------\n", "Conduct market research for TSLA stock\n", "---------- planner ----------\n", "[FunctionCall(id='call_BX5QaRuhmB8CxTsBlqCUIXPb', arguments='{}', name='transfer_to_financial_analyst')]\n", "[Prompt tokens: 169, Completion tokens: 166]\n", "---------- planner ----------\n", "[FunctionExecutionResult(content='Transferred to financial_analyst, adopting the role of financial_analyst immediately.', call_id='call_BX5QaRuhmB8CxTsBlqCUIXPb')]\n", "---------- planner ----------\n", "Transferred to financial_analyst, adopting the role of financial_analyst immediately.\n", "---------- financial_analyst ----------\n", "[FunctionCall(id='call_SAXy1ebtA9mnaZo4ztpD2xHA', arguments='{\"symbol\":\"TSLA\"}', name='get_stock_data')]\n", "[Prompt tokens: 136, Completion tokens: 16]\n", "---------- financial_analyst ----------\n", "[FunctionExecutionResult(content=\"{'price': 180.25, 'volume': 1000000, 'pe_ratio': 65.4, 'market_cap': '700B'}\", call_id='call_SAXy1ebtA9mnaZo4ztpD2xHA')]\n", "---------- financial_analyst ----------\n", "Tool calls:\n", "get_stock_data({\"symbol\":\"TSLA\"}) = {'price': 180.25, 'volume': 1000000, 'pe_ratio': 65.4, 'market_cap': '700B'}\n", "---------- financial_analyst ----------\n", "[FunctionCall(id='call_IsdcFUfBVmtcVzfSuwQpeAwl', arguments='{}', name='transfer_to_planner')]\n", "[Prompt tokens: 199, Completion tokens: 337]\n", "---------- financial_analyst ----------\n", "[FunctionExecutionResult(content='Transferred to planner, adopting the role of planner immediately.', call_id='call_IsdcFUfBVmtcVzfSuwQpeAwl')]\n", "---------- financial_analyst ----------\n", "Transferred to planner, adopting the role of planner immediately.\n", "---------- planner ----------\n", "[FunctionCall(id='call_tN5goNFahrdcSfKnQqT0RONN', arguments='{}', name='transfer_to_news_analyst')]\n", "[Prompt tokens: 291, Completion tokens: 14]\n", "---------- planner ----------\n", "[FunctionExecutionResult(content='Transferred to news_analyst, adopting the role of news_analyst immediately.', call_id='call_tN5goNFahrdcSfKnQqT0RONN')]\n", "---------- planner ----------\n", "Transferred to news_analyst, adopting the role of news_analyst immediately.\n", "---------- news_analyst ----------\n", "[FunctionCall(id='call_Owjw6ZbiPdJgNWMHWxhCKgsp', arguments='{\"query\":\"Tesla market news\"}', name='get_news')]\n", "[Prompt tokens: 235, Completion tokens: 16]\n", "---------- news_analyst ----------\n", "[FunctionExecutionResult(content='[{\\'title\\': \\'Tesla Expands Cybertruck Production\\', \\'date\\': \\'2024-03-20\\', \\'summary\\': \\'Tesla ramps up Cybertruck manufacturing capacity at Gigafactory Texas, aiming to meet strong demand.\\'}, {\\'title\\': \\'Tesla FSD Beta Shows Promise\\', \\'date\\': \\'2024-03-19\\', \\'summary\\': \\'Latest Full Self-Driving beta demonstrates significant improvements in urban navigation and safety features.\\'}, {\\'title\\': \\'Model Y Dominates Global EV Sales\\', \\'date\\': \\'2024-03-18\\', \\'summary\\': \"Tesla\\'s Model Y becomes best-selling electric vehicle worldwide, capturing significant market share.\"}]', call_id='call_Owjw6ZbiPdJgNWMHWxhCKgsp')]\n", "---------- news_analyst ----------\n", "Tool calls:\n", "get_news({\"query\":\"Tesla market news\"}) = [{'title': 'Tesla Expands Cybertruck Production', 'date': '2024-03-20', 'summary': 'Tesla ramps up Cybertruck manufacturing capacity at Gigafactory Texas, aiming to meet strong demand.'}, {'title': 'Tesla FSD Beta Shows Promise', 'date': '2024-03-19', 'summary': 'Latest Full Self-Driving beta demonstrates significant improvements in urban navigation and safety features.'}, {'title': 'Model Y Dominates Global EV Sales', 'date': '2024-03-18', 'summary': \"Tesla's Model Y becomes best-selling electric vehicle worldwide, capturing significant market share.\"}]\n", "---------- news_analyst ----------\n", "Here are some of the key market insights regarding Tesla (TSLA):\n", "\n", "1. **Expansion in Cybertruck Production**: Tesla has increased its Cybertruck production capacity at the Gigafactory in Texas to meet the high demand. This move might positively impact Tesla's revenues if the demand for the Cybertruck continues to grow.\n", "\n", "2. **Advancements in Full Self-Driving (FSD) Technology**: The recent beta release of Tesla's Full Self-Driving software shows significant advancements, particularly in urban navigation and safety. Progress in this area could enhance Tesla's competitive edge in the autonomous driving sector.\n", "\n", "3. **Dominance of Model Y in EV Sales**: Tesla's Model Y has become the best-selling electric vehicle globally, capturing a substantial market share. Such strong sales performance reinforces Tesla's leadership in the electric vehicle market.\n", "\n", "These developments reflect Tesla's ongoing innovation and ability to capture market demand, which could positively influence its stock performance and market position. \n", "\n", "I will now hand off back to the planner.\n", "[Prompt tokens: 398, Completion tokens: 203]\n", "---------- news_analyst ----------\n", "[FunctionCall(id='call_pn7y6PKsBspWA17uOh3AKNMT', arguments='{}', name='transfer_to_planner')]\n", "[Prompt tokens: 609, Completion tokens: 12]\n", "---------- news_analyst ----------\n", "[FunctionExecutionResult(content='Transferred to planner, adopting the role of planner immediately.', call_id='call_pn7y6PKsBspWA17uOh3AKNMT')]\n", "---------- news_analyst ----------\n", "Transferred to planner, adopting the role of planner immediately.\n", "---------- planner ----------\n", "[FunctionCall(id='call_MmXyWuD2uJT64ZdVI5NfhYdX', arguments='{}', name='transfer_to_writer')]\n", "[Prompt tokens: 722, Completion tokens: 11]\n", "---------- planner ----------\n", "[FunctionExecutionResult(content='Transferred to writer, adopting the role of writer immediately.', call_id='call_MmXyWuD2uJT64ZdVI5NfhYdX')]\n", "---------- planner ----------\n", "Transferred to writer, adopting the role of writer immediately.\n", "---------- writer ----------\n", "[FunctionCall(id='call_Pdgu39O6GMYplBiB8jp3uyN3', arguments='{}', name='transfer_to_planner')]\n", "[Prompt tokens: 599, Completion tokens: 323]\n", "---------- writer ----------\n", "[FunctionExecutionResult(content='Transferred to planner, adopting the role of planner immediately.', call_id='call_Pdgu39O6GMYplBiB8jp3uyN3')]\n", "---------- writer ----------\n", "Transferred to planner, adopting the role of planner immediately.\n", "---------- planner ----------\n", "TERMINATE\n", "[Prompt tokens: 772, Completion tokens: 4]\n", "---------- Summary ----------\n", "Number of messages: 27\n", "Finish reason: Text 'TERMINATE' mentioned\n", "Total prompt tokens: 4130\n", "Total completion tokens: 1102\n", "Duration: 17.74 seconds\n"]}, {"data": {"text/plain": ["TaskResult(messages=[TextMessage(source='user', models_usage=None, content='Conduct market research for TSLA stock', type='TextMessage'), ToolCallRequestEvent(source='planner', models_usage=RequestUsage(prompt_tokens=169, completion_tokens=166), content=[FunctionCall(id='call_BX5QaRuhmB8CxTsBlqCUIXPb', arguments='{}', name='transfer_to_financial_analyst')], type='ToolCallRequestEvent'), ToolCallExecutionEvent(source='planner', models_usage=None, content=[FunctionExecutionResult(content='Transferred to financial_analyst, adopting the role of financial_analyst immediately.', call_id='call_BX5QaRuhmB8CxTsBlqCUIXPb')], type='ToolCallExecutionEvent'), HandoffMessage(source='planner', models_usage=None, target='financial_analyst', content='Transferred to financial_analyst, adopting the role of financial_analyst immediately.', type='HandoffMessage'), ToolCallRequestEvent(source='financial_analyst', models_usage=RequestUsage(prompt_tokens=136, completion_tokens=16), content=[FunctionCall(id='call_SAXy1ebtA9mnaZo4ztpD2xHA', arguments='{\"symbol\":\"TSLA\"}', name='get_stock_data')], type='ToolCallRequestEvent'), ToolCallExecutionEvent(source='financial_analyst', models_usage=None, content=[FunctionExecutionResult(content=\"{'price': 180.25, 'volume': 1000000, 'pe_ratio': 65.4, 'market_cap': '700B'}\", call_id='call_SAXy1ebtA9mnaZo4ztpD2xHA')], type='ToolCallExecutionEvent'), TextMessage(source='financial_analyst', models_usage=None, content='Tool calls:\\nget_stock_data({\"symbol\":\"TSLA\"}) = {\\'price\\': 180.25, \\'volume\\': 1000000, \\'pe_ratio\\': 65.4, \\'market_cap\\': \\'700B\\'}', type='TextMessage'), ToolCallRequestEvent(source='financial_analyst', models_usage=RequestUsage(prompt_tokens=199, completion_tokens=337), content=[FunctionCall(id='call_IsdcFUfBVmtcVzfSuwQpeAwl', arguments='{}', name='transfer_to_planner')], type='ToolCallRequestEvent'), ToolCallExecutionEvent(source='financial_analyst', models_usage=None, content=[FunctionExecutionResult(content='Transferred to planner, adopting the role of planner immediately.', call_id='call_IsdcFUfBVmtcVzfSuwQpeAwl')], type='ToolCallExecutionEvent'), HandoffMessage(source='financial_analyst', models_usage=None, target='planner', content='Transferred to planner, adopting the role of planner immediately.', type='HandoffMessage'), ToolCallRequestEvent(source='planner', models_usage=RequestUsage(prompt_tokens=291, completion_tokens=14), content=[FunctionCall(id='call_tN5goNFahrdcSfKnQqT0RONN', arguments='{}', name='transfer_to_news_analyst')], type='ToolCallRequestEvent'), ToolCallExecutionEvent(source='planner', models_usage=None, content=[FunctionExecutionResult(content='Transferred to news_analyst, adopting the role of news_analyst immediately.', call_id='call_tN5goNFahrdcSfKnQqT0RONN')], type='ToolCallExecutionEvent'), HandoffMessage(source='planner', models_usage=None, target='news_analyst', content='Transferred to news_analyst, adopting the role of news_analyst immediately.', type='HandoffMessage'), ToolCallRequestEvent(source='news_analyst', models_usage=RequestUsage(prompt_tokens=235, completion_tokens=16), content=[FunctionCall(id='call_Owjw6ZbiPdJgNWMHWxhCKgsp', arguments='{\"query\":\"Tesla market news\"}', name='get_news')], type='ToolCallRequestEvent'), ToolCallExecutionEvent(source='news_analyst', models_usage=None, content=[FunctionExecutionResult(content='[{\\'title\\': \\'Tesla Expands Cybertruck Production\\', \\'date\\': \\'2024-03-20\\', \\'summary\\': \\'Tesla ramps up Cybertruck manufacturing capacity at Gigafactory Texas, aiming to meet strong demand.\\'}, {\\'title\\': \\'Tesla FSD Beta Shows Promise\\', \\'date\\': \\'2024-03-19\\', \\'summary\\': \\'Latest Full Self-Driving beta demonstrates significant improvements in urban navigation and safety features.\\'}, {\\'title\\': \\'Model Y Dominates Global EV Sales\\', \\'date\\': \\'2024-03-18\\', \\'summary\\': \"Tesla\\'s Model Y becomes best-selling electric vehicle worldwide, capturing significant market share.\"}]', call_id='call_Owjw6ZbiPdJgNWMHWxhCKgsp')], type='ToolCallExecutionEvent'), TextMessage(source='news_analyst', models_usage=None, content='Tool calls:\\nget_news({\"query\":\"Tesla market news\"}) = [{\\'title\\': \\'Tesla Expands Cybertruck Production\\', \\'date\\': \\'2024-03-20\\', \\'summary\\': \\'Tesla ramps up Cybertruck manufacturing capacity at Gigafactory Texas, aiming to meet strong demand.\\'}, {\\'title\\': \\'Tesla FSD Beta Shows Promise\\', \\'date\\': \\'2024-03-19\\', \\'summary\\': \\'Latest Full Self-Driving beta demonstrates significant improvements in urban navigation and safety features.\\'}, {\\'title\\': \\'Model Y Dominates Global EV Sales\\', \\'date\\': \\'2024-03-18\\', \\'summary\\': \"Tesla\\'s Model Y becomes best-selling electric vehicle worldwide, capturing significant market share.\"}]', type='TextMessage'), TextMessage(source='news_analyst', models_usage=RequestUsage(prompt_tokens=398, completion_tokens=203), content=\"Here are some of the key market insights regarding Tesla (TSLA):\\n\\n1. **Expansion in Cybertruck Production**: Tesla has increased its Cybertruck production capacity at the Gigafactory in Texas to meet the high demand. This move might positively impact Tesla's revenues if the demand for the Cybertruck continues to grow.\\n\\n2. **Advancements in Full Self-Driving (FSD) Technology**: The recent beta release of Tesla's Full Self-Driving software shows significant advancements, particularly in urban navigation and safety. Progress in this area could enhance Tesla's competitive edge in the autonomous driving sector.\\n\\n3. **Dominance of Model Y in EV Sales**: Tesla's Model Y has become the best-selling electric vehicle globally, capturing a substantial market share. Such strong sales performance reinforces Tesla's leadership in the electric vehicle market.\\n\\nThese developments reflect Tesla's ongoing innovation and ability to capture market demand, which could positively influence its stock performance and market position. \\n\\nI will now hand off back to the planner.\", type='TextMessage'), ToolCallRequestEvent(source='news_analyst', models_usage=RequestUsage(prompt_tokens=609, completion_tokens=12), content=[FunctionCall(id='call_pn7y6PKsBspWA17uOh3AKNMT', arguments='{}', name='transfer_to_planner')], type='ToolCallRequestEvent'), ToolCallExecutionEvent(source='news_analyst', models_usage=None, content=[FunctionExecutionResult(content='Transferred to planner, adopting the role of planner immediately.', call_id='call_pn7y6PKsBspWA17uOh3AKNMT')], type='ToolCallExecutionEvent'), HandoffMessage(source='news_analyst', models_usage=None, target='planner', content='Transferred to planner, adopting the role of planner immediately.', type='HandoffMessage'), ToolCallRequestEvent(source='planner', models_usage=RequestUsage(prompt_tokens=722, completion_tokens=11), content=[FunctionCall(id='call_MmXyWuD2uJT64ZdVI5NfhYdX', arguments='{}', name='transfer_to_writer')], type='ToolCallRequestEvent'), ToolCallExecutionEvent(source='planner', models_usage=None, content=[FunctionExecutionResult(content='Transferred to writer, adopting the role of writer immediately.', call_id='call_MmXyWuD2uJT64ZdVI5NfhYdX')], type='ToolCallExecutionEvent'), HandoffMessage(source='planner', models_usage=None, target='writer', content='Transferred to writer, adopting the role of writer immediately.', type='HandoffMessage'), ToolCallRequestEvent(source='writer', models_usage=RequestUsage(prompt_tokens=599, completion_tokens=323), content=[FunctionCall(id='call_Pdgu39O6GMYplBiB8jp3uyN3', arguments='{}', name='transfer_to_planner')], type='ToolCallRequestEvent'), ToolCallExecutionEvent(source='writer', models_usage=None, content=[FunctionExecutionResult(content='Transferred to planner, adopting the role of planner immediately.', call_id='call_Pdgu39O6GMYplBiB8jp3uyN3')], type='ToolCallExecutionEvent'), HandoffMessage(source='writer', models_usage=None, target='planner', content='Transferred to planner, adopting the role of planner immediately.', type='HandoffMessage'), TextMessage(source='planner', models_usage=RequestUsage(prompt_tokens=772, completion_tokens=4), content='TERMINATE', type='TextMessage')], stop_reason=\"Text 'TERMINATE' mentioned\")"]}, "execution_count": 12, "metadata": {}, "output_type": "execute_result"}], "source": ["# Define termination condition\n", "text_termination = TextMentionTermination(\"TERMINATE\")\n", "termination = text_termination\n", "\n", "research_team = Swarm(\n", "    participants=[planner, financial_analyst, news_analyst, writer], termination_condition=termination\n", ")\n", "\n", "task = \"Conduct market research for TSLA stock\"\n", "await <PERSON><PERSON>e(research_team.run_stream(task=task))"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": ".venv", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.7"}}, "nbformat": 4, "nbformat_minor": 2}