{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["## Topic and Subscription Example Scenarios\n", "\n", "### Introduction\n", "\n", "In this cookbook, we explore how broadcasting works for agent communication in AutoGen using four different broadcasting scenarios. These scenarios illustrate various ways to handle and distribute messages among agents. We'll use a consistent example of a tax management company processing client requests to demonstrate each scenario.\n", "\n", "### Scenario Overview\n", "\n", "Imagine a tax management company that offers various services to clients, such as tax planning, dispute resolution, compliance, and preparation. The company employs a team of tax specialists, each with expertise in one of these areas, and a tax system manager who oversees the operations.\n", "\n", "Clients submit requests that need to be processed by the appropriate specialists. The communication between the clients, the tax system manager, and the tax specialists is handled through broadcasting in this system.\n", "\n", "We'll explore how different broadcasting scenarios affect the way messages are distributed among agents and how they can be used to tailor the communication flow to specific needs.\n", "\n", "---\n", "\n", "### Broadcasting Scenarios Overview\n", "\n", "We will cover the following broadcasting scenarios:\n", "\n", "1. **Single-Tenant, Single Scope of Publishing**\n", "2. **Multi-Tenant, Single Scope of Publishing**\n", "3. **Single-Tenant, Multiple Scopes of Publishing**\n", "4. **Multi-<PERSON>ant, Multiple Scopes of Publishing**\n", "\n", "\n", "Each scenario represents a different approach to message distribution and agent interaction within the system. By understanding these scenarios, you can design agent communication strategies that best fit your application's requirements."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import asyncio\n", "from dataclasses import dataclass\n", "from enum import Enum\n", "from typing import List\n", "\n", "from autogen_core import (\n", "    MessageContext,\n", "    RoutedAgent,\n", "    SingleThreadedAgentRuntime,\n", "    TopicId,\n", "    TypeSubscription,\n", "    message_handler,\n", ")\n", "from autogen_core._default_subscription import DefaultSubscription\n", "from autogen_core._default_topic import DefaultTopicId\n", "from autogen_core.models import (\n", "    SystemMessage,\n", ")"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [], "source": ["class TaxSpecialty(str, Enum):\n", "    PLANNING = \"planning\"\n", "    DISPUTE_RESOLUTION = \"dispute_resolution\"\n", "    COMPLIANCE = \"compliance\"\n", "    PREPARATION = \"preparation\"\n", "\n", "\n", "@dataclass\n", "class ClientRequest:\n", "    content: str\n", "\n", "\n", "@dataclass\n", "class RequestAssessment:\n", "    content: str\n", "\n", "\n", "class TaxSpecialist(RoutedAgent):\n", "    def __init__(\n", "        self,\n", "        description: str,\n", "        specialty: TaxSpecialty,\n", "        system_messages: List[SystemMessage],\n", "    ) -> None:\n", "        super().__init__(description)\n", "        self.specialty = specialty\n", "        self._system_messages = system_messages\n", "        self._memory: List[ClientRequest] = []\n", "\n", "    @message_handler\n", "    async def handle_message(self, message: ClientRequest, ctx: MessageContext) -> None:\n", "        # Process the client request.\n", "        print(f\"\\n{'='*50}\\nTax specialist {self.id} with specialty {self.specialty}:\\n{message.content}\")\n", "        # Send a response back to the manager\n", "        if ctx.topic_id is None:\n", "            raise ValueError(\"Topic ID is required for broadcasting\")\n", "        await self.publish_message(\n", "            message=RequestAssessment(content=f\"I can handle this request in {self.specialty}.\"),\n", "            topic_id=ctx.topic_id,\n", "        )"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### 1. Single-Tenant, Single Scope of Publishing\n", "\n", "#### Scenarios Explanation\n", "In the single-tenant, single scope of publishing scenario:\n", "\n", "- All agents operate within a single tenant (e.g., one client or user session).\n", "- Messages are published to a single topic, and all agents subscribe to this topic.\n", "- Every agent receives every message that gets published to the topic.\n", "\n", "This scenario is suitable for situations where all agents need to be aware of all messages, and there's no need to isolate communication between different groups of agents or sessions.\n", "\n", "#### Application in the Tax Specialist Company\n", "\n", "In our tax specialist company, this scenario implies:\n", "\n", "- All tax specialists receive every client request and internal message.\n", "- All agents collaborate closely, with full visibility of all communications.\n", "- Useful for tasks or teams where all agents need to be aware of all messages.\n", "\n", "#### How the Scenario Works\n", "\n", "- Subscriptions: All agents use the default subscription(e.g., \"default\").\n", "- Publishing: Messages are published to the default topic.\n", "- Message Handling: Each agent decides whether to act on a message based on its content and available handlers.\n", "\n", "#### Benefits\n", "- Simplicity: Easy to set up and understand.\n", "- Collaboration: Promotes transparency and collaboration among agents.\n", "- Flexibility: Agents can dynamically decide which messages to process.\n", "\n", "#### Considerations\n", "- Scalability: May not scale well with a large number of agents or messages.\n", "- Efficiency: Agents may receive many irrelevant messages, leading to unnecessary processing."]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "==================================================\n", "Tax specialist TaxSpecialist_1:default with specialty TaxSpecialty.PLANNING:\n", "I need to have my tax for 2024 prepared.\n", "\n", "==================================================\n", "Tax specialist TaxSpecialist_2:default with specialty TaxSpecialty.DISPUTE_RESOLUTION:\n", "I need to have my tax for 2024 prepared.\n"]}], "source": ["async def run_single_tenant_single_scope() -> None:\n", "    # Create the runtime.\n", "    runtime = SingleThreadedAgentRuntime()\n", "\n", "    # Register TaxSpecialist agents for each specialty\n", "    specialist_agent_type_1 = \"TaxSpecialist_1\"\n", "    specialist_agent_type_2 = \"TaxSpecialist_2\"\n", "    await TaxSpecialist.register(\n", "        runtime=runtime,\n", "        type=specialist_agent_type_1,\n", "        factory=lambda: TaxSpecialist(\n", "            description=\"A tax specialist 1\",\n", "            specialty=TaxSpecialty.PLANNING,\n", "            system_messages=[SystemMessage(content=\"You are a tax specialist.\")],\n", "        ),\n", "    )\n", "\n", "    await TaxSpecialist.register(\n", "        runtime=runtime,\n", "        type=specialist_agent_type_2,\n", "        factory=lambda: TaxSpecialist(\n", "            description=\"A tax specialist 2\",\n", "            specialty=TaxSpecialty.DISPUTE_RESOLUTION,\n", "            system_messages=[SystemMessage(content=\"You are a tax specialist.\")],\n", "        ),\n", "    )\n", "\n", "    # Add default subscriptions for each agent type\n", "    await runtime.add_subscription(DefaultSubscription(agent_type=specialist_agent_type_1))\n", "    await runtime.add_subscription(DefaultSubscription(agent_type=specialist_agent_type_2))\n", "\n", "    # Start the runtime and send a message to agents on default topic\n", "    runtime.start()\n", "    await runtime.publish_message(ClientRequest(\"I need to have my tax for 2024 prepared.\"), topic_id=DefaultTopicId())\n", "    await runtime.stop_when_idle()\n", "\n", "\n", "await run_single_tenant_single_scope()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### 2. <PERSON><PERSON><PERSON><PERSON>, Single Scope of Publishing\n", "\n", "#### Scenario Explanation\n", "\n", "In the multi-tenant, single scope of publishing scenario:\n", "\n", "- There are multiple tenants (e.g., multiple clients or user sessions).\n", "- Each tenant has its own isolated topic through the topic source.\n", "- All agents within a tenant subscribe to the tenant's topic. If needed, new agent instances are created for each tenant.\n", "- Messages are only visible to agents within the same tenant.\n", "\n", "This scenario is useful when you need to isolate communication between different tenants but want all agents within a tenant to be aware of all messages.\n", "\n", "#### Application in the Tax Specialist Company\n", "\n", "In this scenario:\n", "\n", "- The company serves multiple clients (tenants) simultaneously.\n", "- For each client, a dedicated set of agent instances is created.\n", "- Each client's communication is isolated from others.\n", "- All agents for a client receive messages published to that client's topic.\n", "\n", "#### How the Scenario Works\n", "\n", "- Subscriptions: Agents subscribe to topics based on the tenant's identity.\n", "- Publishing: Messages are published to the tenant-specific topic.\n", "- Message Handling: Agents only receive messages relevant to their tenant.\n", "\n", "#### Benefits\n", "- Tenant Isolation: Ensures data privacy and separation between clients.\n", "- Collaboration Within Tenant: Agents can collaborate freely within their tenant.\n", "\n", "#### Considerations\n", "- Complexity: Requires managing multiple sets of agents and topics.\n", "- Resource Usage: More agent instances may consume additional resources."]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "==================================================\n", "Tax specialist TaxSpecialist_planning:ClientABC with specialty TaxSpecialty.PLANNING:\n", "ClientABC requires tax services.\n", "\n", "==================================================\n", "Tax specialist TaxSpecialist_dispute_resolution:ClientABC with specialty TaxSpecialty.DISPUTE_RESOLUTION:\n", "ClientABC requires tax services.\n", "\n", "==================================================\n", "Tax specialist TaxSpecialist_compliance:ClientABC with specialty TaxSpecialty.COMPLIANCE:\n", "ClientABC requires tax services.\n", "\n", "==================================================\n", "Tax specialist TaxSpecialist_preparation:ClientABC with specialty TaxSpecialty.PREPARATION:\n", "ClientABC requires tax services.\n", "\n", "==================================================\n", "Tax specialist TaxSpecialist_planning:ClientXYZ with specialty TaxSpecialty.PLANNING:\n", "ClientXYZ requires tax services.\n", "\n", "==================================================\n", "Tax specialist TaxSpecialist_dispute_resolution:ClientXYZ with specialty TaxSpecialty.DISPUTE_RESOLUTION:\n", "ClientXYZ requires tax services.\n", "\n", "==================================================\n", "Tax specialist TaxSpecialist_compliance:ClientXYZ with specialty TaxSpecialty.COMPLIANCE:\n", "ClientXYZ requires tax services.\n", "\n", "==================================================\n", "Tax specialist TaxSpecialist_preparation:ClientXYZ with specialty TaxSpecialty.PREPARATION:\n", "ClientXYZ requires tax services.\n"]}], "source": ["async def run_multi_tenant_single_scope() -> None:\n", "    # Create the runtime\n", "    runtime = SingleThreadedAgentRuntime()\n", "\n", "    # List of clients (tenants)\n", "    tenants = [\"ClientABC\", \"ClientXYZ\"]\n", "\n", "    # Initialize sessions and map the topic type to each TaxSpecialist agent type\n", "    for specialty in TaxSpecialty:\n", "        specialist_agent_type = f\"TaxSpecialist_{specialty.value}\"\n", "        await TaxSpecialist.register(\n", "            runtime=runtime,\n", "            type=specialist_agent_type,\n", "            factory=lambda specialty=specialty: TaxSpecialist(  # type: ignore\n", "                description=f\"A tax specialist in {specialty.value}.\",\n", "                specialty=specialty,\n", "                system_messages=[SystemMessage(content=f\"You are a tax specialist in {specialty.value}.\")],\n", "            ),\n", "        )\n", "        specialist_subscription = DefaultSubscription(agent_type=specialist_agent_type)\n", "        await runtime.add_subscription(specialist_subscription)\n", "\n", "    # Start the runtime\n", "    runtime.start()\n", "\n", "    # Publish client requests to their respective topics\n", "    for tenant in tenants:\n", "        topic_source = tenant  # The topic source is the client name\n", "        topic_id = DefaultTopicId(source=topic_source)\n", "        await runtime.publish_message(\n", "            ClientRequest(f\"{tenant} requires tax services.\"),\n", "            topic_id=topic_id,\n", "        )\n", "\n", "    # Allow time for message processing\n", "    await asyncio.sleep(1)\n", "\n", "    # Stop the runtime when idle\n", "    await runtime.stop_when_idle()\n", "\n", "\n", "await run_multi_tenant_single_scope()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### 3. Single-Tenant, <PERSON> Scopes of Publishing\n", "\n", "#### Scenario Explanation\n", "\n", "In the single-tenant, multiple scopes of publishing scenario:\n", "\n", "- All agents operate within a single tenant.\n", "- Messages are published to different topics.\n", "- Agents subscribe to specific topics relevant to their role or specialty.\n", "- Messages are directed to subsets of agents based on the topic.\n", "\n", "This scenario allows for targeted communication within a tenant, enabling more granular control over message distribution.\n", "\n", "#### Application in the Tax Management Company\n", "\n", "In this scenario:\n", "\n", "- The tax system manager communicates with specific specialists based on their specialties.\n", "- Different topics represent different specialties (e.g., \"planning\", \"compliance\").\n", "- Specialists subscribe only to the topic that matches their specialty.\n", "- The manager publishes messages to specific topics to reach the intended specialists.\n", "\n", "#### How the Scenario Works\n", "\n", "- Subscriptions: Agents subscribe to topics corresponding to their specialties.\n", "- Publishing: Messages are published to topics based on the intended recipients.\n", "- Message Handling: Only agents subscribed to a topic receive its messages.\n", "#### Benefits\n", "\n", "- Targeted Communication: Messages reach only the relevant agents.\n", "- Efficiency: Reduces unnecessary message processing by agents.\n", "\n", "#### Considerations\n", "\n", "- Setup Complexity: Requires careful management of topics and subscriptions.\n", "- Flexibility: Changes in communication scenarios may require updating subscriptions."]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "==================================================\n", "Tax specialist TaxSpecialist_planning:default with specialty TaxSpecialty.PLANNING:\n", "I need assistance with planning taxes.\n", "\n", "==================================================\n", "Tax specialist TaxSpecialist_dispute_resolution:default with specialty TaxSpecialty.DISPUTE_RESOLUTION:\n", "I need assistance with dispute_resolution taxes.\n", "\n", "==================================================\n", "Tax specialist TaxSpecialist_compliance:default with specialty TaxSpecialty.COMPLIANCE:\n", "I need assistance with compliance taxes.\n", "\n", "==================================================\n", "Tax specialist TaxSpecialist_preparation:default with specialty TaxSpecialty.PREPARATION:\n", "I need assistance with preparation taxes.\n"]}], "source": ["async def run_single_tenant_multiple_scope() -> None:\n", "    # Create the runtime\n", "    runtime = SingleThreadedAgentRuntime()\n", "    # Register TaxSpecialist agents for each specialty and add subscriptions\n", "    for specialty in TaxSpecialty:\n", "        specialist_agent_type = f\"TaxSpecialist_{specialty.value}\"\n", "        await TaxSpecialist.register(\n", "            runtime=runtime,\n", "            type=specialist_agent_type,\n", "            factory=lambda specialty=specialty: TaxSpecialist(  # type: ignore\n", "                description=f\"A tax specialist in {specialty.value}.\",\n", "                specialty=specialty,\n", "                system_messages=[SystemMessage(content=f\"You are a tax specialist in {specialty.value}.\")],\n", "            ),\n", "        )\n", "        specialist_subscription = TypeSubscription(topic_type=specialty.value, agent_type=specialist_agent_type)\n", "        await runtime.add_subscription(specialist_subscription)\n", "\n", "    # Start the runtime\n", "    runtime.start()\n", "\n", "    # Publish a ClientRequest to each specialist's topic\n", "    for specialty in TaxSpecialty:\n", "        topic_id = TopicId(type=specialty.value, source=\"default\")\n", "        await runtime.publish_message(\n", "            ClientRequest(f\"I need assistance with {specialty.value} taxes.\"),\n", "            topic_id=topic_id,\n", "        )\n", "\n", "    # Allow time for message processing\n", "    await asyncio.sleep(1)\n", "\n", "    # Stop the runtime when idle\n", "    await runtime.stop_when_idle()\n", "\n", "\n", "await run_single_tenant_multiple_scope()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### 4. <PERSON><PERSON><PERSON><PERSON>, <PERSON> Scopes of Publishing\n", "\n", "#### Scenario Explanation\n", "\n", "In the multi-tenant, multiple scopes of publishing scenario:\n", "\n", "- There are multiple tenants, each with their own set of agents.\n", "- Messages are published to multiple topics within each tenant.\n", "- Agents subscribe to tenant-specific topics relevant to their role.\n", "- Combines tenant isolation with targeted communication.\n", "\n", "This scenario provides the highest level of control over message distribution, suitable for complex systems with multiple clients and specialized communication needs.\n", "\n", "#### Application in the Tax Management Company\n", "\n", "In this scenario:\n", "\n", "- The company serves multiple clients, each with dedicated agent instances.\n", "- Within each client, agents communicate using multiple topics based on specialties.\n", "- For example, Client A's planning specialist subscribes to the \"planning\" topic with source \"ClientA\".\n", "- The tax system manager for each client communicates with their specialists using tenant-specific topics.\n", "\n", "#### How the Scenario Works\n", "\n", "- Subscriptions: Agents subscribe to topics based on both tenant identity and specialty.\n", "- Publishing: Messages are published to tenant-specific and specialty-specific topics.\n", "- Message Handling: Only agents matching the tenant and topic receive messages.\n", "\n", "#### Benefits\n", "\n", "- Complete Isolation: Ensures both tenant and communication isolation.\n", "- Granular Control: Enables precise routing of messages to intended agents.\n", "\n", "#### Considerations\n", "\n", "- Complexity: Requires careful management of topics, tenants, and subscriptions.\n", "- Resource Usage: Increased number of agent instances and topics may impact resources."]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "==================================================\n", "Tax specialist TaxSpecialist_planning:ClientABC with specialty TaxSpecialty.PLANNING:\n", "ClientABC needs assistance with planning taxes.\n", "\n", "==================================================\n", "Tax specialist TaxSpecialist_dispute_resolution:ClientABC with specialty TaxSpecialty.DISPUTE_RESOLUTION:\n", "ClientABC needs assistance with dispute_resolution taxes.\n", "\n", "==================================================\n", "Tax specialist TaxSpecialist_compliance:ClientABC with specialty TaxSpecialty.COMPLIANCE:\n", "ClientABC needs assistance with compliance taxes.\n", "\n", "==================================================\n", "Tax specialist TaxSpecialist_preparation:ClientABC with specialty TaxSpecialty.PREPARATION:\n", "ClientABC needs assistance with preparation taxes.\n", "\n", "==================================================\n", "Tax specialist TaxSpecialist_planning:ClientXYZ with specialty TaxSpecialty.PLANNING:\n", "ClientXYZ needs assistance with planning taxes.\n", "\n", "==================================================\n", "Tax specialist TaxSpecialist_dispute_resolution:ClientXYZ with specialty TaxSpecialty.DISPUTE_RESOLUTION:\n", "ClientXYZ needs assistance with dispute_resolution taxes.\n", "\n", "==================================================\n", "Tax specialist TaxSpecialist_compliance:ClientXYZ with specialty TaxSpecialty.COMPLIANCE:\n", "ClientXYZ needs assistance with compliance taxes.\n", "\n", "==================================================\n", "Tax specialist TaxSpecialist_preparation:ClientXYZ with specialty TaxSpecialty.PREPARATION:\n", "ClientXYZ needs assistance with preparation taxes.\n"]}], "source": ["async def run_multi_tenant_multiple_scope() -> None:\n", "    # Create the runtime\n", "    runtime = SingleThreadedAgentRuntime()\n", "\n", "    # Define TypeSubscriptions for each specialty and tenant\n", "    tenants = [\"ClientABC\", \"ClientXYZ\"]\n", "\n", "    # Initialize agents for all specialties and add type subscriptions\n", "    for specialty in TaxSpecialty:\n", "        specialist_agent_type = f\"TaxSpecialist_{specialty.value}\"\n", "        await TaxSpecialist.register(\n", "            runtime=runtime,\n", "            type=specialist_agent_type,\n", "            factory=lambda specialty=specialty: TaxSpecialist(  # type: ignore\n", "                description=f\"A tax specialist in {specialty.value}.\",\n", "                specialty=specialty,\n", "                system_messages=[SystemMessage(content=f\"You are a tax specialist in {specialty.value}.\")],\n", "            ),\n", "        )\n", "        for tenant in tenants:\n", "            specialist_subscription = TypeSubscription(\n", "                topic_type=f\"{tenant}_{specialty.value}\", agent_type=specialist_agent_type\n", "            )\n", "            await runtime.add_subscription(specialist_subscription)\n", "\n", "    # Start the runtime\n", "    runtime.start()\n", "\n", "    # Send messages for each tenant to each specialty\n", "    for tenant in tenants:\n", "        for specialty in TaxSpecialty:\n", "            topic_id = TopicId(type=f\"{tenant}_{specialty.value}\", source=tenant)\n", "            await runtime.publish_message(\n", "                ClientRequest(f\"{tenant} needs assistance with {specialty.value} taxes.\"),\n", "                topic_id=topic_id,\n", "            )\n", "\n", "    # Allow time for message processing\n", "    await asyncio.sleep(1)\n", "\n", "    # Stop the runtime when idle\n", "    await runtime.stop_when_idle()\n", "\n", "\n", "await run_multi_tenant_multiple_scope()"]}], "metadata": {"kernelspec": {"display_name": ".venv", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.6"}}, "nbformat": 4, "nbformat_minor": 2}