/autogen/,/autogen/stable/
/autogen/docs/Getting-Started,/autogen/0.2/docs/Getting-Started
/autogen/docs/installation/,/autogen/0.2/docs/installation/
/autogen/docs/tutorial/introduction,/autogen/0.2/docs/tutorial/introduction
/autogen/docs/topics,/autogen/0.2/docs/topics
/autogen/docs/reference/agentchat/conversable_agent,/autogen/0.2/docs/reference/agentchat/conversable_agent
/autogen/docs/FAQ,/autogen/0.2/docs/FAQ
/autogen/docs/autogen-studio/getting-started,/autogen/0.2/docs/autogen-studio/getting-started
/autogen/docs/ecosystem,/autogen/0.2/docs/ecosystem
/autogen/docs/contributor-guide/contributing,/autogen/0.2/docs/contributor-guide/contributing
/autogen/docs/Research,/autogen/0.2/docs/Research
/autogen/docs/Examples,/autogen/0.2/docs/Examples
/autogen/docs/notebooks,/autogen/0.2/docs/notebooks
/autogen/docs/Gallery,/autogen/0.2/docs/Gallery
/autogen/blog,/autogen/0.2/blog
/autogen/docs/Use-Cases/agent_chat,/autogen/0.2/docs/Use-Cases/agent_chat
/autogen/docs/Use-Cases/enhanced_inference,/autogen/0.2/docs/Use-Cases/enhanced_inference
/autogen/docs/tutorial,/autogen/0.2/docs/tutorial
/autogen/docs/tutorial/chat-termination,/autogen/0.2/docs/tutorial/chat-termination
/autogen/docs/tutorial/human-in-the-loop,/autogen/0.2/docs/tutorial/human-in-the-loop
/autogen/docs/tutorial/code-executors,/autogen/0.2/docs/tutorial/code-executors
/autogen/docs/tutorial/tool-use,/autogen/0.2/docs/tutorial/tool-use
/autogen/docs/tutorial/conversation-patterns,/autogen/0.2/docs/tutorial/conversation-patterns
/autogen/docs/tutorial/what-next,/autogen/0.2/docs/tutorial/what-next
/autogen/docs/topics/code-execution/cli-code-executor,/autogen/0.2/docs/topics/code-execution/cli-code-executor
/autogen/docs/topics/openai-assistant/gpt_assistant_agent,/autogen/0.2/docs/topics/openai-assistant/gpt_assistant_agent
/autogen/docs/topics/groupchat/customized_speaker_selection,/autogen/0.2/docs/topics/groupchat/customized_speaker_selection
/autogen/docs/topics/non-openai-models/about-using-nonopenai-models,/autogen/0.2/docs/topics/non-openai-models/about-using-nonopenai-models
/autogen/docs/topics/handling_long_contexts/compressing_text_w_llmligua,/autogen/0.2/docs/topics/handling_long_contexts/compressing_text_w_llmligua
/autogen/docs/topics/llm-caching,/autogen/0.2/docs/topics/llm-caching
/autogen/docs/topics/llm-observability,/autogen/0.2/docs/topics/llm-observability
/autogen/docs/topics/llm_configuration,/autogen/0.2/docs/topics/llm_configuration
/autogen/docs/topics/prompting-and-reasoning/react,/autogen/0.2/docs/topics/prompting-and-reasoning/react
/autogen/docs/topics/retrieval_augmentation,/autogen/0.2/docs/topics/retrieval_augmentation
/autogen/docs/topics/task_decomposition,/autogen/0.2/docs/topics/task_decomposition
/autogen/docs/autogen-studio,/autogen/0.2/docs/autogen-studio
/autogen/docs/contributor-guide,/autogen/0.2/docs/contributor-guide
/autogen/docs/Migration-Guide,/autogen/0.2/docs/Migration-Guide
/autogen/docs/reference/agentchat/conversable_agent/,/autogen/0.2/docs/reference/agentchat/conversable_agent/
/autogen/docs/installation/Docker,/autogen/0.2/docs/installation/Docker
/autogen/docs/installation/Optional-Dependencies,/autogen/0.2/docs/installation/Optional-Dependencies
/autogen/docs/reference/agentchat/contrib/agent_eval/,/autogen/0.2/docs/reference/agentchat/contrib/agent_eval/
/autogen/docs/reference/agentchat/agent,/autogen/0.2/docs/reference/agentchat/agent
/autogen/docs/reference/agentchat/assistant_agent,/autogen/0.2/docs/reference/agentchat/assistant_agent
/autogen/docs/reference/agentchat/chat,/autogen/0.2/docs/reference/agentchat/chat
/autogen/docs/reference/agentchat/groupchat,/autogen/0.2/docs/reference/agentchat/groupchat
/autogen/docs/reference/agentchat/user_proxy_agent,/autogen/0.2/docs/reference/agentchat/user_proxy_agent
/autogen/docs/reference/agentchat/utils,/autogen/0.2/docs/reference/agentchat/utils
/autogen/docs/reference/browser_utils/abstract_markdown_browser,/autogen/0.2/docs/reference/browser_utils/abstract_markdown_browser
/autogen/docs/reference/cache/abstract_cache_base,/autogen/0.2/docs/reference/cache/abstract_cache_base
/autogen/docs/reference/coding/jupyter/base,/autogen/0.2/docs/reference/coding/jupyter/base
/autogen/docs/reference/io/base,/autogen/0.2/docs/reference/io/base
/autogen/docs/reference/logger/base_logger,/autogen/0.2/docs/reference/logger/base_logger
/autogen/docs/reference/oai/anthropic,/autogen/0.2/docs/reference/oai/anthropic
/autogen/docs/reference/code_utils,/autogen/0.2/docs/reference/code_utils
/autogen/docs/reference/exception_utils,/autogen/0.2/docs/reference/exception_utils
/autogen/docs/reference/function_utils,/autogen/0.2/docs/reference/function_utils
/autogen/docs/reference/graph_utils,/autogen/0.2/docs/reference/graph_utils
/autogen/docs/reference/math_utils,/autogen/0.2/docs/reference/math_utils
/autogen/docs/reference/retrieve_utils,/autogen/0.2/docs/reference/retrieve_utils
/autogen/docs/reference/runtime_logging,/autogen/0.2/docs/reference/runtime_logging
/autogen/docs/reference/token_count_utils,/autogen/0.2/docs/reference/token_count_utils
/autogen/docs/reference/oai/client,/autogen/0.2/docs/reference/oai/client
/autogen/blog/2023/07/14/Local-LLMs,/autogen/0.2/blog/2023/07/14/Local-LLMs
/autogen/blog/2024/01/26/Custom-Models,/autogen/0.2/blog/2024/01/26/Custom-Models
/autogen/docs/autogen-studio/usage,/autogen/0.2/docs/autogen-studio/usage
/autogen/docs/autogen-studio/faqs,/autogen/0.2/docs/autogen-studio/faqs
/autogen/docs/ecosystem/agentops,/autogen/0.2/docs/ecosystem/agentops
/autogen/docs/ecosystem/azure_cosmos_db,/autogen/0.2/docs/ecosystem/azure_cosmos_db
/autogen/docs/ecosystem/composio,/autogen/0.2/docs/ecosystem/composio
/autogen/docs/ecosystem/databricks,/autogen/0.2/docs/ecosystem/databricks
/autogen/docs/ecosystem/llamaindex,/autogen/0.2/docs/ecosystem/llamaindex
/autogen/docs/ecosystem/mem0,/autogen/0.2/docs/ecosystem/mem0
/autogen/docs/ecosystem/memgpt,/autogen/0.2/docs/ecosystem/memgpt
/autogen/docs/ecosystem/microsoft-fabric,/autogen/0.2/docs/ecosystem/microsoft-fabric
/autogen/docs/ecosystem/ollama,/autogen/0.2/docs/ecosystem/ollama
/autogen/docs/ecosystem/pgvector,/autogen/0.2/docs/ecosystem/pgvector
/autogen/docs/ecosystem/portkey,/autogen/0.2/docs/ecosystem/portkey
/autogen/docs/ecosystem/promptflow,/autogen/0.2/docs/ecosystem/promptflow
/autogen/docs/contributor-guide/docker,/autogen/0.2/docs/contributor-guide/docker
/autogen/docs/contributor-guide/documentation,/autogen/0.2/docs/contributor-guide/documentation
/autogen/docs/contributor-guide/file-bug-report,/autogen/0.2/docs/contributor-guide/file-bug-report
/autogen/docs/contributor-guide/maintainer,/autogen/0.2/docs/contributor-guide/maintainer
/autogen/docs/contributor-guide/pre-commit,/autogen/0.2/docs/contributor-guide/pre-commit
/autogen/docs/contributor-guide/tests,/autogen/0.2/docs/contributor-guide/tests
/autogen/docs/notebooks/agentchat_auto_feedback_from_code_execution,/autogen/0.2/docs/notebooks/agentchat_auto_feedback_from_code_execution
/autogen/docs/notebooks/agentchat_RetrieveChat,/autogen/0.2/docs/notebooks/agentchat_RetrieveChat
/autogen/docs/notebooks/agentchat_RetrieveChat_qdrant,/autogen/0.2/docs/notebooks/agentchat_RetrieveChat_qdrant
/autogen/docs/notebooks/agentchat_groupchat,/autogen/0.2/docs/notebooks/agentchat_groupchat
/autogen/docs/notebooks/agentchat_groupchat_vis,/autogen/0.2/docs/notebooks/agentchat_groupchat_vis
/autogen/docs/notebooks/agentchat_groupchat_research,/autogen/0.2/docs/notebooks/agentchat_groupchat_research
/autogen/docs/notebooks/agentchat_groupchat_finite_state_machine,/autogen/0.2/docs/notebooks/agentchat_groupchat_finite_state_machine
/autogen/docs/notebooks/agentchat_society_of_mind,/autogen/0.2/docs/notebooks/agentchat_society_of_mind
/autogen/docs/notebooks/agentchat_groupchat_customized,/autogen/0.2/docs/notebooks/agentchat_groupchat_customized
/autogen/docs/notebooks/agentchat_multi_task_chats,/autogen/0.2/docs/notebooks/agentchat_multi_task_chats
/autogen/docs/notebooks/agentchat_multi_task_async_chats,/autogen/0.2/docs/notebooks/agentchat_multi_task_async_chats
/autogen/docs/notebooks/agentchats_sequential_chats,/autogen/0.2/docs/notebooks/agentchats_sequential_chats
/autogen/docs/notebooks/agentchat_nestedchat,/autogen/0.2/docs/notebooks/agentchat_nestedchat
/autogen/docs/notebooks/agentchat_nested_sequential_chats,/autogen/0.2/docs/notebooks/agentchat_nested_sequential_chats
/autogen/docs/notebooks/agentchat_nestedchat_optiguide,/autogen/0.2/docs/notebooks/agentchat_nestedchat_optiguide
/autogen/docs/notebooks/agentchat_nested_chats_chess,/autogen/0.2/docs/notebooks/agentchat_nested_chats_chess
/autogen/docs/notebooks/agentchat_function_call_currency_calculator,/autogen/0.2/docs/notebooks/agentchat_function_call_currency_calculator
/autogen/docs/notebooks/agentchat_function_call_async,/autogen/0.2/docs/notebooks/agentchat_function_call_async
/autogen/docs/notebooks/agentchat_groupchat_RAG,/autogen/0.2/docs/notebooks/agentchat_groupchat_RAG
/autogen/docs/notebooks/agentchat_video_transcript_translate_with_whisper,/autogen/0.2/docs/notebooks/agentchat_video_transcript_translate_with_whisper
/autogen/docs/notebooks/agentchat_webscraping_with_apify,/autogen/0.2/docs/notebooks/agentchat_webscraping_with_apify
/autogen/docs/notebooks/agentchat_teaching,/autogen/0.2/docs/notebooks/agentchat_teaching
/autogen/docs/notebooks/agentchat_teachability,/autogen/0.2/docs/notebooks/agentchat_teachability
/autogen/docs/notebooks/agentchat_nested_chats_chess_altmodels,/autogen/0.2/docs/notebooks/agentchat_nested_chats_chess_altmodels
/autogen/docs/notebooks/agentchat_transform_messages,/autogen/0.2/docs/notebooks/agentchat_transform_messages
/autogen/docs/Use-Cases/enhanced_inference/,/autogen/0.2/docs/Use-Cases/enhanced_inference/
/autogen/docs/notebooks/JSON_mode_example,/autogen/0.2/docs/notebooks/JSON_mode_example
/autogen/docs/notebooks/agentchat_RetrieveChat_mongodb,/autogen/0.2/docs/notebooks/agentchat_RetrieveChat_mongodb
/autogen/docs/notebooks/agentchat_RetrieveChat_pgvector,/autogen/0.2/docs/notebooks/agentchat_RetrieveChat_pgvector
/autogen/docs/notebooks/agentchat_agentops,/autogen/0.2/docs/notebooks/agentchat_agentops
/autogen/docs/notebooks/agentchat_agentoptimizer,/autogen/0.2/docs/notebooks/agentchat_agentoptimizer
/autogen/docs/notebooks/agentchat_azr_ai_search,/autogen/0.2/docs/notebooks/agentchat_azr_ai_search
/autogen/docs/notebooks/agentchat_custom_model,/autogen/0.2/docs/notebooks/agentchat_custom_model
/autogen/docs/notebooks/agentchat_databricks_dbrx,/autogen/0.2/docs/notebooks/agentchat_databricks_dbrx
/autogen/docs/notebooks/agentchat_function_call_code_writing,/autogen/0.2/docs/notebooks/agentchat_function_call_code_writing
/autogen/docs/notebooks/agentchat_function_call_with_composio,/autogen/0.2/docs/notebooks/agentchat_function_call_with_composio
/autogen/docs/notebooks/agentchat_group_chat_with_llamaindex_agents,/autogen/0.2/docs/notebooks/agentchat_group_chat_with_llamaindex_agents
/autogen/docs/notebooks/agentchat_groupchat_stateflow,/autogen/0.2/docs/notebooks/agentchat_groupchat_stateflow
/autogen/docs/notebooks/agentchat_image_generation_capability,/autogen/0.2/docs/notebooks/agentchat_image_generation_capability
/autogen/docs/notebooks/agentchat_lmm_gpt-4v,/autogen/0.2/docs/notebooks/agentchat_lmm_gpt-4v
/autogen/docs/notebooks/agentchat_logging,/autogen/0.2/docs/notebooks/agentchat_logging
/autogen/docs/notebooks/agentchat_memory_using_mem0,/autogen/0.2/docs/notebooks/agentchat_memory_using_mem0
/autogen/docs/notebooks/agentchat_oai_assistant_function_call,/autogen/0.2/docs/notebooks/agentchat_oai_assistant_function_call
/autogen/docs/notebooks/agentchat_oai_assistant_groupchat,/autogen/0.2/docs/notebooks/agentchat_oai_assistant_groupchat
/autogen/docs/notebooks/agentchat_oai_code_interpreter,/autogen/0.2/docs/notebooks/agentchat_oai_code_interpreter
/autogen/docs/notebooks/agentchat_websockets,/autogen/0.2/docs/notebooks/agentchat_websockets
/autogen/docs/notebooks/gpt_assistant_agent_function_call,/autogen/0.2/docs/notebooks/gpt_assistant_agent_function_call
/autogen/blog/2024/10/02/new-autogen-architecture-preview,/autogen/0.2/blog/2024/10/02/new-autogen-architecture-preview
/autogen/blog/2024/07/25/AgentOps,/autogen/0.2/blog/2024/07/25/AgentOps
/autogen/blog/2024/06/24/AltModels-Classes,/autogen/0.2/blog/2024/06/24/AltModels-Classes
/autogen/blog/2024/06/21/AgentEval,/autogen/0.2/blog/2024/06/21/AgentEval
/autogen/blog/2024/05/24/Agent,/autogen/0.2/blog/2024/05/24/Agent
/autogen/blog/2024/03/11/AutoDefense/Defending%20LLMs%20Against%20Jailbreak%20Attacks%20with%20AutoDefense,/autogen/0.2/blog/2024/03/11/AutoDefense/Defending%20LLMs%20Against%20Jailbreak%20Attacks%20with%20AutoDefense
/autogen/blog/2024/03/03/AutoGen-Update,/autogen/0.2/blog/2024/03/03/AutoGen-Update
/autogen/blog/2024/02/29/StateFlow,/autogen/0.2/blog/2024/02/29/StateFlow
/autogen/blog/2024/02/11/FSM-GroupChat,/autogen/0.2/blog/2024/02/11/FSM-GroupChat
/autogen/blog/2024/02/02/AutoAnny,/autogen/0.2/blog/2024/02/02/AutoAnny
/autogen/blog/2024/01/25/AutoGenBench,/autogen/0.2/blog/2024/01/25/AutoGenBench
/autogen/blog/2024/01/23/Code-execution-in-docker,/autogen/0.2/blog/2024/01/23/Code-execution-in-docker
/autogen/blog/2023/12/29/AgentDescriptions,/autogen/0.2/blog/2023/12/29/AgentDescriptions
/autogen/blog/2023/12/23/AgentOptimizer,/autogen/0.2/blog/2023/12/23/AgentOptimizer
/autogen/blog/2023/12/01/AutoGenStudio,/autogen/0.2/blog/2023/12/01/AutoGenStudio
/autogen/blog/2023/11/26/Agent-AutoBuild,/autogen/0.2/blog/2023/11/26/Agent-AutoBuild
/autogen/blog/2023/11/20/AgentEval,/autogen/0.2/blog/2023/11/20/AgentEval
/autogen/blog/2023/11/13/OAI-assistants,/autogen/0.2/blog/2023/11/13/OAI-assistants
/autogen/blog/2023/11/09/EcoAssistant,/autogen/0.2/blog/2023/11/09/EcoAssistant
/autogen/blog/2023/11/06/LMM-Agent,/autogen/0.2/blog/2023/11/06/LMM-Agent
/autogen/blog/2023/10/26/TeachableAgent,/autogen/0.2/blog/2023/10/26/TeachableAgent
/autogen/blog/2023/10/18/RetrieveChat,/autogen/0.2/blog/2023/10/18/RetrieveChat
/autogen/blog/2023/06/28/MathChat,/autogen/0.2/blog/2023/06/28/MathChat
/autogen/blog/2023/05/18/GPT-adaptive-humaneval,/autogen/0.2/blog/2023/05/18/GPT-adaptive-humaneval
/autogen/blog/2023/04/21/LLM-tuning-math,/autogen/0.2/blog/2023/04/21/LLM-tuning-math
/autogen/blog/tags/auto-gen,/autogen/0.2/blog/tags/auto-gen
/autogen/docs/notebooks/agentchat_agentops/,/autogen/0.2/docs/notebooks/agentchat_agentops/
/autogen/blog/tags/llm,/autogen/0.2/blog/tags/llm
/autogen/blog/tags/agent,/autogen/0.2/blog/tags/agent
/autogen/blog/tags/observability,/autogen/0.2/blog/tags/observability
/autogen/blog/tags/agent-ops,/autogen/0.2/blog/tags/agent-ops
/autogen/docs/topics/non-openai-models/cloud-gemini,/autogen/0.2/docs/topics/non-openai-models/cloud-gemini
/autogen/docs/topics/handling_long_contexts/intro_to_transform_messages,/autogen/0.2/docs/topics/handling_long_contexts/intro_to_transform_messages
/autogen/docs/reference/oai/gemini,/autogen/0.2/docs/reference/oai/gemini
/autogen/blog/tags/mistral-ai,/autogen/0.2/blog/tags/mistral-ai
/autogen/blog/tags/anthropic,/autogen/0.2/blog/tags/anthropic
/autogen/blog/tags/together-ai,/autogen/0.2/blog/tags/together-ai
/autogen/blog/tags/gemini,/autogen/0.2/blog/tags/gemini
/autogen/blog/2023/11/20/AgentEval/,/autogen/0.2/blog/2023/11/20/AgentEval/
/autogen/blog/tags/gpt,/autogen/0.2/blog/tags/gpt
/autogen/blog/tags/evaluation,/autogen/0.2/blog/tags/evaluation
/autogen/blog/tags/task-utility,/autogen/0.2/blog/tags/task-utility
/autogen/docs/topics/prompting-and-reasoning/reflection,/autogen/0.2/docs/topics/prompting-and-reasoning/reflection
/autogen/docs/topics/code-execution/user-defined-functions,/autogen/0.2/docs/topics/code-execution/user-defined-functions
/autogen/blog/2023/12/01/AutoGenStudio/,/autogen/0.2/blog/2023/12/01/AutoGenStudio/
/autogen/blog/tags/thoughts,/autogen/0.2/blog/tags/thoughts
/autogen/blog/tags/interview-notes,/autogen/0.2/blog/tags/interview-notes
/autogen/blog/tags/research,/autogen/0.2/blog/tags/research
/autogen/blog/tags/news,/autogen/0.2/blog/tags/news
/autogen/blog/tags/summary,/autogen/0.2/blog/tags/summary
/autogen/blog/tags/roadmap,/autogen/0.2/blog/tags/roadmap
/autogen/blog/2024/02/11/FSM-GroupChat/,/autogen/0.2/blog/2024/02/11/FSM-GroupChat/
/autogen/docs/notebooks/agentchat_groupchat_finite_state_machine/,/autogen/0.2/docs/notebooks/agentchat_groupchat_finite_state_machine/
/autogen/blog/page/2,/autogen/0.2/blog/page/2
/autogen/docs/reference/coding/local_commandline_code_executor,/autogen/0.2/docs/reference/coding/local_commandline_code_executor
/autogen/docs/reference/coding/docker_commandline_code_executor,/autogen/0.2/docs/reference/coding/docker_commandline_code_executor
/autogen/docs/reference/coding/jupyter/jupyter_code_executor,/autogen/0.2/docs/reference/coding/jupyter/jupyter_code_executor
/autogen/docs/topics/code-execution/jupyter-code-executor,/autogen/0.2/docs/topics/code-execution/jupyter-code-executor
/autogen/docs/topics/code-execution/custom-executor,/autogen/0.2/docs/topics/code-execution/custom-executor
/autogen/docs/topics/groupchat/resuming_groupchat,/autogen/0.2/docs/topics/groupchat/resuming_groupchat
/autogen/docs/topics/groupchat/transform_messages_speaker_selection,/autogen/0.2/docs/topics/groupchat/transform_messages_speaker_selection
/autogen/docs/tags/orchestration,/autogen/0.2/docs/tags/orchestration
/autogen/docs/tags/group-chat,/autogen/0.2/docs/tags/group-chat
/autogen/docs/topics/non-openai-models/best-tips-for-nonopenai-models,/autogen/0.2/docs/topics/non-openai-models/best-tips-for-nonopenai-models
/autogen/docs/topics/non-openai-models/cloud-anthropic,/autogen/0.2/docs/topics/non-openai-models/cloud-anthropic
/autogen/docs/topics/non-openai-models/cloud-bedrock,/autogen/0.2/docs/topics/non-openai-models/cloud-bedrock
/autogen/docs/topics/non-openai-models/cloud-cerebras,/autogen/0.2/docs/topics/non-openai-models/cloud-cerebras
/autogen/docs/topics/non-openai-models/cloud-cohere,/autogen/0.2/docs/topics/non-openai-models/cloud-cohere
/autogen/docs/topics/non-openai-models/cloud-gemini_vertexai,/autogen/0.2/docs/topics/non-openai-models/cloud-gemini_vertexai
/autogen/docs/topics/non-openai-models/cloud-groq,/autogen/0.2/docs/topics/non-openai-models/cloud-groq
/autogen/docs/topics/non-openai-models/cloud-mistralai,/autogen/0.2/docs/topics/non-openai-models/cloud-mistralai
/autogen/docs/topics/non-openai-models/cloud-togetherai,/autogen/0.2/docs/topics/non-openai-models/cloud-togetherai
/autogen/docs/topics/non-openai-models/local-litellm-ollama,/autogen/0.2/docs/topics/non-openai-models/local-litellm-ollama
/autogen/docs/topics/non-openai-models/local-lm-studio,/autogen/0.2/docs/topics/non-openai-models/local-lm-studio
/autogen/docs/topics/non-openai-models/local-ollama,/autogen/0.2/docs/topics/non-openai-models/local-ollama
/autogen/docs/topics/non-openai-models/local-vllm,/autogen/0.2/docs/topics/non-openai-models/local-vllm
/autogen/docs/topics/non-openai-models/transforms-for-nonopenai-models,/autogen/0.2/docs/topics/non-openai-models/transforms-for-nonopenai-models
/autogen/docs/notebooks/agentchat_custom_model/,/autogen/0.2/docs/notebooks/agentchat_custom_model/
/autogen/docs/reference/cache/disk_cache,/autogen/0.2/docs/reference/cache/disk_cache
/autogen/docs/reference/cache/redis_cache,/autogen/0.2/docs/reference/cache/redis_cache
/autogen/docs/reference/oai/openai_utils,/autogen/0.2/docs/reference/oai/openai_utils
/autogen/docs/reference/cache/,/autogen/0.2/docs/reference/cache/
/autogen/docs/reference/agentchat/contrib/retrieve_user_proxy_agent,/autogen/0.2/docs/reference/agentchat/contrib/retrieve_user_proxy_agent
/autogen/docs/reference/agentchat/contrib/agent_eval/criterion,/autogen/0.2/docs/reference/agentchat/contrib/agent_eval/criterion
/autogen/docs/reference/agentchat/contrib/agent_eval/critic_agent,/autogen/0.2/docs/reference/agentchat/contrib/agent_eval/critic_agent
/autogen/docs/reference/agentchat/contrib/agent_eval/quantifier_agent,/autogen/0.2/docs/reference/agentchat/contrib/agent_eval/quantifier_agent
/autogen/docs/reference/agentchat/contrib/agent_eval/subcritic_agent,/autogen/0.2/docs/reference/agentchat/contrib/agent_eval/subcritic_agent
/autogen/docs/reference/agentchat/contrib/agent_eval/task,/autogen/0.2/docs/reference/agentchat/contrib/agent_eval/task
/autogen/docs/reference/agentchat/contrib/capabilities/agent_capability,/autogen/0.2/docs/reference/agentchat/contrib/capabilities/agent_capability
/autogen/docs/reference/agentchat/contrib/graph_rag/document,/autogen/0.2/docs/reference/agentchat/contrib/graph_rag/document
/autogen/docs/reference/agentchat/contrib/vectordb/base,/autogen/0.2/docs/reference/agentchat/contrib/vectordb/base
/autogen/docs/reference/agentchat/contrib/agent_builder,/autogen/0.2/docs/reference/agentchat/contrib/agent_builder
/autogen/docs/reference/agentchat/contrib/agent_optimizer,/autogen/0.2/docs/reference/agentchat/contrib/agent_optimizer
/autogen/docs/reference/agentchat/contrib/gpt_assistant_agent,/autogen/0.2/docs/reference/agentchat/contrib/gpt_assistant_agent
/autogen/docs/reference/agentchat/contrib/img_utils,/autogen/0.2/docs/reference/agentchat/contrib/img_utils
/autogen/docs/reference/agentchat/contrib/llamaindex_conversable_agent,/autogen/0.2/docs/reference/agentchat/contrib/llamaindex_conversable_agent
/autogen/docs/reference/agentchat/contrib/llava_agent,/autogen/0.2/docs/reference/agentchat/contrib/llava_agent
/autogen/docs/reference/agentchat/contrib/math_user_proxy_agent,/autogen/0.2/docs/reference/agentchat/contrib/math_user_proxy_agent
/autogen/docs/reference/agentchat/contrib/multimodal_conversable_agent,/autogen/0.2/docs/reference/agentchat/contrib/multimodal_conversable_agent
/autogen/docs/reference/agentchat/contrib/qdrant_retrieve_user_proxy_agent,/autogen/0.2/docs/reference/agentchat/contrib/qdrant_retrieve_user_proxy_agent
/autogen/docs/reference/agentchat/contrib/retrieve_assistant_agent,/autogen/0.2/docs/reference/agentchat/contrib/retrieve_assistant_agent
/autogen/docs/reference/agentchat/contrib/society_of_mind_agent,/autogen/0.2/docs/reference/agentchat/contrib/society_of_mind_agent
/autogen/docs/reference/agentchat/contrib/text_analyzer_agent,/autogen/0.2/docs/reference/agentchat/contrib/text_analyzer_agent
/autogen/docs/reference/agentchat/contrib/web_surfer,/autogen/0.2/docs/reference/agentchat/contrib/web_surfer
/autogen/docs/reference/browser_utils/markdown_search,/autogen/0.2/docs/reference/browser_utils/markdown_search
/autogen/docs/reference/browser_utils/mdconvert,/autogen/0.2/docs/reference/browser_utils/mdconvert
/autogen/docs/reference/browser_utils/playwright_markdown_browser,/autogen/0.2/docs/reference/browser_utils/playwright_markdown_browser
/autogen/docs/reference/browser_utils/requests_markdown_browser,/autogen/0.2/docs/reference/browser_utils/requests_markdown_browser
/autogen/docs/reference/browser_utils/selenium_markdown_browser,/autogen/0.2/docs/reference/browser_utils/selenium_markdown_browser
/autogen/docs/reference/cache/cache_factory,/autogen/0.2/docs/reference/cache/cache_factory
/autogen/docs/reference/cache/cosmos_db_cache,/autogen/0.2/docs/reference/cache/cosmos_db_cache
/autogen/docs/reference/cache/in_memory_cache,/autogen/0.2/docs/reference/cache/in_memory_cache
/autogen/docs/reference/coding/jupyter/docker_jupyter_server,/autogen/0.2/docs/reference/coding/jupyter/docker_jupyter_server
/autogen/docs/reference/coding/jupyter/embedded_ipython_code_executor,/autogen/0.2/docs/reference/coding/jupyter/embedded_ipython_code_executor
/autogen/docs/reference/coding/jupyter/jupyter_client,/autogen/0.2/docs/reference/coding/jupyter/jupyter_client
/autogen/docs/reference/coding/jupyter/local_jupyter_server,/autogen/0.2/docs/reference/coding/jupyter/local_jupyter_server
/autogen/docs/reference/coding/base,/autogen/0.2/docs/reference/coding/base
/autogen/docs/reference/coding/factory,/autogen/0.2/docs/reference/coding/factory
/autogen/docs/reference/coding/func_with_reqs,/autogen/0.2/docs/reference/coding/func_with_reqs
/autogen/docs/reference/coding/markdown_code_extractor,/autogen/0.2/docs/reference/coding/markdown_code_extractor
/autogen/docs/reference/coding/utils,/autogen/0.2/docs/reference/coding/utils
/autogen/docs/reference/io/console,/autogen/0.2/docs/reference/io/console
/autogen/docs/reference/io/websockets,/autogen/0.2/docs/reference/io/websockets
/autogen/docs/reference/logger/file_logger,/autogen/0.2/docs/reference/logger/file_logger
/autogen/docs/reference/oai/bedrock,/autogen/0.2/docs/reference/oai/bedrock
/autogen/docs/reference/oai/cerebras,/autogen/0.2/docs/reference/oai/cerebras
/autogen/docs/reference/oai/client_utils,/autogen/0.2/docs/reference/oai/client_utils
/autogen/docs/reference/oai/cohere,/autogen/0.2/docs/reference/oai/cohere
/autogen/docs/reference/oai/completion,/autogen/0.2/docs/reference/oai/completion
/autogen/docs/reference/oai/groq,/autogen/0.2/docs/reference/oai/groq
/autogen/docs/reference/oai/mistral,/autogen/0.2/docs/reference/oai/mistral
/autogen/docs/reference/oai/ollama,/autogen/0.2/docs/reference/oai/ollama
/autogen/docs/reference/oai/rate_limiters,/autogen/0.2/docs/reference/oai/rate_limiters
/autogen/docs/reference/oai/together,/autogen/0.2/docs/reference/oai/together
/autogen/docs/Contribute,/autogen/0.2/docs/Contribute
/autogen/docs/tags/code-generation,/autogen/0.2/docs/tags/code-generation
/autogen/docs/tags/debugging,/autogen/0.2/docs/tags/debugging
/autogen/docs/tags/rag,/autogen/0.2/docs/tags/rag
/autogen/docs/tags/nested-chat,/autogen/0.2/docs/tags/nested-chat
/autogen/docs/tags/sequential-chats,/autogen/0.2/docs/tags/sequential-chats
/autogen/docs/tags/hierarchical-chat,/autogen/0.2/docs/tags/hierarchical-chat
/autogen/docs/tags/tool-use,/autogen/0.2/docs/tags/tool-use
/autogen/docs/tags/function-call,/autogen/0.2/docs/tags/function-call
/autogen/docs/tags/async,/autogen/0.2/docs/tags/async
/autogen/docs/tags/whisper,/autogen/0.2/docs/tags/whisper
/autogen/docs/tags/web-scraping,/autogen/0.2/docs/tags/web-scraping
/autogen/docs/tags/apify,/autogen/0.2/docs/tags/apify
/autogen/docs/tags/teaching,/autogen/0.2/docs/tags/teaching
/autogen/docs/tags/teachability,/autogen/0.2/docs/tags/teachability
/autogen/docs/tags/capability,/autogen/0.2/docs/tags/capability
/autogen/docs/tags/long-context-handling,/autogen/0.2/docs/tags/long-context-handling
/autogen/blog/2023/12/29/AgentDescriptions/,/autogen/0.2/blog/2023/12/29/AgentDescriptions/
/autogen/docs/tags/json,/autogen/0.2/docs/tags/json
/autogen/docs/tags/description,/autogen/0.2/docs/tags/description
/autogen/docs/tags/prompt-hacking,/autogen/0.2/docs/tags/prompt-hacking
/autogen/docs/tags/monitoring,/autogen/0.2/docs/tags/monitoring
/autogen/docs/tags/optimization,/autogen/0.2/docs/tags/optimization
/autogen/docs/tags/tool-function,/autogen/0.2/docs/tags/tool-function
/autogen/docs/tags/azure-identity,/autogen/0.2/docs/tags/azure-identity
/autogen/docs/tags/azure-ai-search,/autogen/0.2/docs/tags/azure-ai-search
/autogen/docs/tags/custom-model,/autogen/0.2/docs/tags/custom-model
/autogen/docs/topics/non-openai-models/cloud-mistralai/,/autogen/0.2/docs/topics/non-openai-models/cloud-mistralai/
/autogen/docs/tutorial/conversation-patterns/,/autogen/0.2/docs/tutorial/conversation-patterns/
/autogen/docs/tags/dbrx,/autogen/0.2/docs/tags/dbrx
/autogen/docs/tags/databricks,/autogen/0.2/docs/tags/databricks
/autogen/docs/tags/open-source,/autogen/0.2/docs/tags/open-source
/autogen/docs/tags/lakehouse,/autogen/0.2/docs/tags/lakehouse
/autogen/docs/tags/data-intelligence,/autogen/0.2/docs/tags/data-intelligence
/autogen/docs/tags/software-engineering,/autogen/0.2/docs/tags/software-engineering
/autogen/docs/tags/agents,/autogen/0.2/docs/tags/agents
/autogen/docs/tags/react,/autogen/0.2/docs/tags/react
/autogen/docs/tags/llama-index,/autogen/0.2/docs/tags/llama-index
/autogen/docs/tags/research,/autogen/0.2/docs/tags/research
/autogen/docs/tags/multimodal,/autogen/0.2/docs/tags/multimodal
/autogen/docs/tags/gpt-4-v,/autogen/0.2/docs/tags/gpt-4-v
/autogen/docs/tags/logging,/autogen/0.2/docs/tags/logging
/autogen/docs/tags/memory,/autogen/0.2/docs/tags/memory
/autogen/docs/tags/open-ai-assistant,/autogen/0.2/docs/tags/open-ai-assistant
/autogen/docs/tags/code-interpreter,/autogen/0.2/docs/tags/code-interpreter
/autogen/docs/reference/io/base/IOStream,/autogen/0.2/docs/reference/io/base/IOStream
/autogen/docs/reference/io/websockets/IOWebsockets,/autogen/0.2/docs/reference/io/websockets/IOWebsockets
/autogen/docs/tags/websockets,/autogen/0.2/docs/tags/websockets
/autogen/docs/tags/streaming,/autogen/0.2/docs/tags/streaming
/autogen/docs/tags/gpt-assistant,/autogen/0.2/docs/tags/gpt-assistant
/autogen/docs/Installation,/autogen/0.2/docs/Installation
/autogen/docs/reference/agentchat/agentchat/,/autogen/0.2/docs/reference/agentchat/agentchat/
/autogen/blog/tags/ui,/autogen/0.2/blog/tags/ui
/autogen/blog/tags/web,/autogen/0.2/blog/tags/web
/autogen/blog/tags/ux,/autogen/0.2/blog/tags/ux
/autogen/blog/tags/openai-assistant,/autogen/0.2/blog/tags/openai-assistant
/autogen/blog/tags/rag,/autogen/0.2/blog/tags/rag
/autogen/blog/tags/cost-effectiveness,/autogen/0.2/blog/tags/cost-effectiveness
/autogen/blog/tags/lmm,/autogen/0.2/blog/tags/lmm
/autogen/blog/tags/multimodal,/autogen/0.2/blog/tags/multimodal
/autogen/blog/tags/teach,/autogen/0.2/blog/tags/teach
/autogen/blog/tags,/autogen/0.2/blog/tags
/autogen/blog/tags/llm/page/2,/autogen/0.2/blog/tags/llm/page/2
/autogen/docs/tags/gemini,/autogen/0.2/docs/tags/gemini
/autogen/blog/page/3,/autogen/0.2/blog/page/3
/autogen/docs/tags/resume,/autogen/0.2/docs/tags/resume
/autogen/docs/reference/agentchat/contrib/capabilities/transform_messages,/autogen/0.2/docs/reference/agentchat/contrib/capabilities/transform_messages
/autogen/docs/tags,/autogen/0.2/docs/tags
/autogen/docs/contributor-guide/contributing/,/autogen/0.2/docs/contributor-guide/contributing/
/autogen/docs/tags/vertexai,/autogen/0.2/docs/tags/vertexai
/autogen/docs/installation,/autogen/0.2/docs/installation
/autogen/docs/reference/agentchat/contrib/capabilities/generate_images,/autogen/0.2/docs/reference/agentchat/contrib/capabilities/generate_images
/autogen/docs/reference/agentchat/contrib/capabilities/teachability,/autogen/0.2/docs/reference/agentchat/contrib/capabilities/teachability
/autogen/docs/reference/agentchat/contrib/capabilities/text_compressors,/autogen/0.2/docs/reference/agentchat/contrib/capabilities/text_compressors
/autogen/docs/reference/agentchat/contrib/capabilities/transforms,/autogen/0.2/docs/reference/agentchat/contrib/capabilities/transforms
/autogen/docs/reference/agentchat/contrib/capabilities/transforms_util,/autogen/0.2/docs/reference/agentchat/contrib/capabilities/transforms_util
/autogen/docs/reference/agentchat/contrib/capabilities/vision_capability,/autogen/0.2/docs/reference/agentchat/contrib/capabilities/vision_capability
/autogen/docs/reference/agentchat/contrib/graph_rag/graph_query_engine,/autogen/0.2/docs/reference/agentchat/contrib/graph_rag/graph_query_engine
/autogen/docs/reference/agentchat/contrib/graph_rag/graph_rag_capability,/autogen/0.2/docs/reference/agentchat/contrib/graph_rag/graph_rag_capability
/autogen/docs/reference/agentchat/contrib/vectordb/chromadb,/autogen/0.2/docs/reference/agentchat/contrib/vectordb/chromadb
/autogen/docs/reference/agentchat/contrib/vectordb/couchbase,/autogen/0.2/docs/reference/agentchat/contrib/vectordb/couchbase
/autogen/docs/reference/agentchat/contrib/vectordb/mongodb,/autogen/0.2/docs/reference/agentchat/contrib/vectordb/mongodb
/autogen/docs/reference/agentchat/contrib/vectordb/pgvectordb,/autogen/0.2/docs/reference/agentchat/contrib/vectordb/pgvectordb
/autogen/docs/reference/agentchat/contrib/vectordb/qdrant,/autogen/0.2/docs/reference/agentchat/contrib/vectordb/qdrant
/autogen/docs/reference/agentchat/contrib/vectordb/utils,/autogen/0.2/docs/reference/agentchat/contrib/vectordb/utils
/autogen/0.4.0dev0/,/autogen/0.4.0.dev0/
/autogen/0.4.0dev1/,/autogen/0.4.0.dev1/
