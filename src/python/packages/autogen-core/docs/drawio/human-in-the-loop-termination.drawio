<mxfile host="app.diagrams.net" agent="Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********" version="24.8.9">
  <diagram id="W6FvA7JMSRd8l6w4kv95" name="Page-1">
    <mxGraphModel dx="1645" dy="1089" grid="1" gridSize="10" guides="1" tooltips="1" connect="1" arrows="1" fold="1" page="1" pageScale="1" pageWidth="850" pageHeight="1100" math="0" shadow="0">
      <root>
        <mxCell id="0" />
        <mxCell id="1" parent="0" />
        <mxCell id="sYyRPZP6EHBJkb5ZCHJR-1" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;exitX=0.25;exitY=1;exitDx=0;exitDy=0;entryX=0.5;entryY=1;entryDx=0;entryDy=0;fontStyle=0;strokeWidth=2;" parent="1" source="sYyRPZP6EHBJkb5ZCHJR-2" target="sYyRPZP6EHBJkb5ZCHJR-12" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="sYyRPZP6EHBJkb5ZCHJR-2" value="" style="rounded=0;whiteSpace=wrap;html=1;fillColor=#f5f5f5;strokeColor=#666666;fontColor=#333333;" parent="1" vertex="1">
          <mxGeometry x="250" y="450" width="300" height="260" as="geometry" />
        </mxCell>
        <mxCell id="sYyRPZP6EHBJkb5ZCHJR-3" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;exitX=1;exitY=0.25;exitDx=0;exitDy=0;entryX=0;entryY=0.5;entryDx=0;entryDy=0;" parent="1" source="sYyRPZP6EHBJkb5ZCHJR-6" target="sYyRPZP6EHBJkb5ZCHJR-8" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="sYyRPZP6EHBJkb5ZCHJR-4" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;exitX=1;exitY=0.5;exitDx=0;exitDy=0;entryX=0;entryY=0.5;entryDx=0;entryDy=0;" parent="1" source="sYyRPZP6EHBJkb5ZCHJR-6" target="sYyRPZP6EHBJkb5ZCHJR-7" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="sYyRPZP6EHBJkb5ZCHJR-5" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;exitX=1;exitY=0.75;exitDx=0;exitDy=0;entryX=0;entryY=0.5;entryDx=0;entryDy=0;" parent="1" source="sYyRPZP6EHBJkb5ZCHJR-6" target="sYyRPZP6EHBJkb5ZCHJR-9" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="sYyRPZP6EHBJkb5ZCHJR-6" value="" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#d5e8d4;strokeColor=#82b366;verticalAlign=middle;" parent="1" vertex="1">
          <mxGeometry x="280" y="520" width="100" height="120" as="geometry" />
        </mxCell>
        <mxCell id="sYyRPZP6EHBJkb5ZCHJR-7" value="Agent 2" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#dae8fc;strokeColor=#6c8ebf;" parent="1" vertex="1">
          <mxGeometry x="420" y="550" width="100" height="60" as="geometry" />
        </mxCell>
        <mxCell id="sYyRPZP6EHBJkb5ZCHJR-8" value="Agent 1" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#dae8fc;strokeColor=#6c8ebf;" parent="1" vertex="1">
          <mxGeometry x="420" y="470" width="100" height="60" as="geometry" />
        </mxCell>
        <mxCell id="sYyRPZP6EHBJkb5ZCHJR-9" value="&lt;div&gt;Agent 3&lt;/div&gt;" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#dae8fc;strokeColor=#6c8ebf;" parent="1" vertex="1">
          <mxGeometry x="420" y="630" width="100" height="60" as="geometry" />
        </mxCell>
        <mxCell id="sYyRPZP6EHBJkb5ZCHJR-10" value="RoundRobinGroupChat" style="text;html=1;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;" parent="1" vertex="1">
          <mxGeometry x="255" y="460" width="150" height="30" as="geometry" />
        </mxCell>
        <mxCell id="sYyRPZP6EHBJkb5ZCHJR-11" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;entryX=0.25;entryY=0;entryDx=0;entryDy=0;exitX=0.5;exitY=0;exitDx=0;exitDy=0;fontStyle=0;strokeWidth=2;" parent="1" source="sYyRPZP6EHBJkb5ZCHJR-12" target="sYyRPZP6EHBJkb5ZCHJR-2" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="sYyRPZP6EHBJkb5ZCHJR-12" value="Application/User" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#ffe6cc;strokeColor=#d79b00;" parent="1" vertex="1">
          <mxGeometry x="70" y="540" width="120" height="80" as="geometry" />
        </mxCell>
        <mxCell id="sYyRPZP6EHBJkb5ZCHJR-13" value="Task/Feedback" style="text;html=1;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontStyle=1" parent="1" vertex="1">
          <mxGeometry x="40" y="500" width="90" height="30" as="geometry" />
        </mxCell>
        <mxCell id="sYyRPZP6EHBJkb5ZCHJR-14" value="TaskResult" style="text;html=1;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontStyle=1" parent="1" vertex="1">
          <mxGeometry x="60" y="650" width="60" height="30" as="geometry" />
        </mxCell>
        <mxCell id="sYyRPZP6EHBJkb5ZCHJR-15" value="&lt;b&gt;Termination&lt;/b&gt;&lt;div&gt;&lt;b&gt;Condition&lt;/b&gt;&lt;/div&gt;" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#fff2cc;strokeColor=#d6b656;" parent="1" vertex="1">
          <mxGeometry x="285" y="580" width="90" height="50" as="geometry" />
        </mxCell>
        <mxCell id="sYyRPZP6EHBJkb5ZCHJR-16" value="&lt;span style=&quot;text-wrap-mode: wrap;&quot;&gt;Orchestrator&lt;/span&gt;" style="text;html=1;align=center;verticalAlign=middle;resizable=0;points=[];autosize=1;strokeColor=none;fillColor=none;" parent="1" vertex="1">
          <mxGeometry x="285" y="530" width="90" height="30" as="geometry" />
        </mxCell>
        <mxCell id="dF9Xe1aUO8W1j2Y2Youd-1" value="Starts / Resumes the Team" style="text;html=1;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontStyle=1" vertex="1" parent="1">
          <mxGeometry x="150" y="400" width="160" height="30" as="geometry" />
        </mxCell>
        <mxCell id="dF9Xe1aUO8W1j2Y2Youd-2" value="Saves the Team&#39;s State" style="text;html=1;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontStyle=1" vertex="1" parent="1">
          <mxGeometry x="162.5" y="730" width="135" height="30" as="geometry" />
        </mxCell>
      </root>
    </mxGraphModel>
  </diagram>
</mxfile>
