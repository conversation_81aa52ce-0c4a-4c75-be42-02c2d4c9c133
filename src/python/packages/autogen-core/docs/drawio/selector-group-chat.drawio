<mxfile host="app.diagrams.net" agent="Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********" version="24.8.6">
  <diagram name="Page-1" id="IxvBbn9yzyvW_p07RlV3">
    <mxGraphModel dx="1796" dy="1158" grid="1" gridSize="10" guides="1" tooltips="1" connect="1" arrows="1" fold="1" page="1" pageScale="1" pageWidth="850" pageHeight="1100" math="0" shadow="0">
      <root>
        <mxCell id="0" />
        <mxCell id="1" parent="0" />
        <mxCell id="_di9dOpueXQmeiIHWsOV-6" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;exitX=0.25;exitY=1;exitDx=0;exitDy=0;entryX=0.5;entryY=1;entryDx=0;entryDy=0;" edge="1" parent="1" source="_di9dOpueXQmeiIHWsOV-1" target="_di9dOpueXQmeiIHWsOV-4">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="_di9dOpueXQmeiIHWsOV-1" value="" style="rounded=0;whiteSpace=wrap;html=1;fillColor=#f5f5f5;strokeColor=#666666;fontColor=#333333;" vertex="1" parent="1">
          <mxGeometry x="240" y="440" width="300" height="260" as="geometry" />
        </mxCell>
        <mxCell id="_di9dOpueXQmeiIHWsOV-21" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;exitX=1;exitY=0.25;exitDx=0;exitDy=0;entryX=0;entryY=0.5;entryDx=0;entryDy=0;" edge="1" parent="1" source="XTbiivpkQXd5a4I06BR3-9" target="XTbiivpkQXd5a4I06BR3-14">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="_di9dOpueXQmeiIHWsOV-22" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;exitX=1;exitY=0.5;exitDx=0;exitDy=0;entryX=0;entryY=0.5;entryDx=0;entryDy=0;" edge="1" parent="1" source="XTbiivpkQXd5a4I06BR3-9" target="XTbiivpkQXd5a4I06BR3-13">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="_di9dOpueXQmeiIHWsOV-23" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;exitX=1;exitY=0.75;exitDx=0;exitDy=0;entryX=0;entryY=0.5;entryDx=0;entryDy=0;" edge="1" parent="1" source="XTbiivpkQXd5a4I06BR3-9" target="XTbiivpkQXd5a4I06BR3-15">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="XTbiivpkQXd5a4I06BR3-9" value="Selector" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#d5e8d4;strokeColor=#82b366;verticalAlign=middle;" parent="1" vertex="1">
          <mxGeometry x="270" y="520" width="100" height="100" as="geometry" />
        </mxCell>
        <mxCell id="XTbiivpkQXd5a4I06BR3-13" value="Web Search Agent" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#dae8fc;strokeColor=#6c8ebf;" parent="1" vertex="1">
          <mxGeometry x="410" y="540" width="100" height="60" as="geometry" />
        </mxCell>
        <mxCell id="XTbiivpkQXd5a4I06BR3-14" value="Planning Agent" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#dae8fc;strokeColor=#6c8ebf;" parent="1" vertex="1">
          <mxGeometry x="410" y="460" width="100" height="60" as="geometry" />
        </mxCell>
        <mxCell id="XTbiivpkQXd5a4I06BR3-15" value="Data Analyst&lt;div&gt;Agent&lt;/div&gt;" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#dae8fc;strokeColor=#6c8ebf;" parent="1" vertex="1">
          <mxGeometry x="410" y="620" width="100" height="60" as="geometry" />
        </mxCell>
        <mxCell id="_di9dOpueXQmeiIHWsOV-2" value="SelectorGroupChat" style="text;html=1;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;" vertex="1" parent="1">
          <mxGeometry x="245" y="450" width="150" height="30" as="geometry" />
        </mxCell>
        <mxCell id="_di9dOpueXQmeiIHWsOV-5" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;entryX=0.25;entryY=0;entryDx=0;entryDy=0;exitX=0.5;exitY=0;exitDx=0;exitDy=0;" edge="1" parent="1" source="_di9dOpueXQmeiIHWsOV-4" target="_di9dOpueXQmeiIHWsOV-1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="_di9dOpueXQmeiIHWsOV-4" value="Application/User" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#ffe6cc;strokeColor=#d79b00;" vertex="1" parent="1">
          <mxGeometry x="60" y="530" width="120" height="80" as="geometry" />
        </mxCell>
        <mxCell id="_di9dOpueXQmeiIHWsOV-7" value="Task" style="text;html=1;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;" vertex="1" parent="1">
          <mxGeometry x="200" y="390" width="60" height="30" as="geometry" />
        </mxCell>
        <mxCell id="_di9dOpueXQmeiIHWsOV-8" value="TaskResult" style="text;html=1;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;" vertex="1" parent="1">
          <mxGeometry x="200" y="720" width="60" height="30" as="geometry" />
        </mxCell>
      </root>
    </mxGraphModel>
  </diagram>
</mxfile>
