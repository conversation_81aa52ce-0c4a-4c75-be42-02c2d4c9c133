<mxfile host="app.diagrams.net" agent="Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********" version="24.7.6">
  <diagram name="Page-1" id="kM63aGWDAVgwnXhMnwsJ">
    <mxGraphModel dx="1773" dy="1145" grid="1" gridSize="10" guides="1" tooltips="1" connect="1" arrows="1" fold="1" page="1" pageScale="1" pageWidth="850" pageHeight="1100" math="0" shadow="0">
      <root>
        <mxCell id="0" />
        <mxCell id="1" parent="0" />
        <mxCell id="rwyUPL19n1b9p3f1DsXw-6" value="approved=False" style="text;html=1;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;" vertex="1" parent="1">
          <mxGeometry x="360" y="290" width="60" height="30" as="geometry" />
        </mxCell>
        <mxCell id="rwyUPL19n1b9p3f1DsXw-11" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;entryX=0;entryY=0.5;entryDx=0;entryDy=0;" edge="1" parent="1" source="rwyUPL19n1b9p3f1DsXw-8" target="rwyUPL19n1b9p3f1DsXw-10">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="rwyUPL19n1b9p3f1DsXw-8" value="CoderAgent:&lt;div&gt;handle_writing_task&lt;/div&gt;" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#ffe6cc;strokeColor=#d79b00;" vertex="1" parent="1">
          <mxGeometry x="160" y="200" width="120" height="60" as="geometry" />
        </mxCell>
        <mxCell id="rwyUPL19n1b9p3f1DsXw-16" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;entryX=0;entryY=0.5;entryDx=0;entryDy=0;" edge="1" parent="1" source="rwyUPL19n1b9p3f1DsXw-10" target="rwyUPL19n1b9p3f1DsXw-15">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="rwyUPL19n1b9p3f1DsXw-10" value="ReviewerAgent:&lt;div&gt;handle_review_task&lt;/div&gt;" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#d5e8d4;strokeColor=#82b366;" vertex="1" parent="1">
          <mxGeometry x="305" y="200" width="120" height="60" as="geometry" />
        </mxCell>
        <mxCell id="rwyUPL19n1b9p3f1DsXw-17" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;entryX=0.5;entryY=1;entryDx=0;entryDy=0;" edge="1" parent="1" source="rwyUPL19n1b9p3f1DsXw-15" target="rwyUPL19n1b9p3f1DsXw-8">
          <mxGeometry relative="1" as="geometry">
            <Array as="points">
              <mxPoint x="540" y="320" />
              <mxPoint x="220" y="320" />
            </Array>
          </mxGeometry>
        </mxCell>
        <mxCell id="rwyUPL19n1b9p3f1DsXw-20" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;entryX=0;entryY=0.5;entryDx=0;entryDy=0;" edge="1" parent="1" source="rwyUPL19n1b9p3f1DsXw-15" target="rwyUPL19n1b9p3f1DsXw-21">
          <mxGeometry relative="1" as="geometry">
            <mxPoint x="630" y="230" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="rwyUPL19n1b9p3f1DsXw-15" value="CoderAgent:&lt;div&gt;handle_review_result&lt;/div&gt;" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#ffe6cc;strokeColor=#d79b00;" vertex="1" parent="1">
          <mxGeometry x="450" y="200" width="120" height="60" as="geometry" />
        </mxCell>
        <mxCell id="rwyUPL19n1b9p3f1DsXw-18" value="approved=True" style="text;html=1;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;" vertex="1" parent="1">
          <mxGeometry x="580" y="245" width="60" height="30" as="geometry" />
        </mxCell>
        <mxCell id="rwyUPL19n1b9p3f1DsXw-21" value="Application:&lt;div&gt;Receive Result&lt;/div&gt;" style="text;html=1;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;" vertex="1" parent="1">
          <mxGeometry x="620" y="215" width="90" height="30" as="geometry" />
        </mxCell>
        <mxCell id="rwyUPL19n1b9p3f1DsXw-23" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;entryX=0;entryY=0.5;entryDx=0;entryDy=0;" edge="1" parent="1" source="rwyUPL19n1b9p3f1DsXw-22" target="rwyUPL19n1b9p3f1DsXw-8">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="rwyUPL19n1b9p3f1DsXw-22" value="Application:&lt;div&gt;Send Task&lt;/div&gt;" style="text;html=1;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;" vertex="1" parent="1">
          <mxGeometry x="80" y="215" width="60" height="30" as="geometry" />
        </mxCell>
        <mxCell id="rwyUPL19n1b9p3f1DsXw-27" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;exitX=1;exitY=0.25;exitDx=0;exitDy=0;entryX=0;entryY=0.25;entryDx=0;entryDy=0;" edge="1" parent="1" source="rwyUPL19n1b9p3f1DsXw-25" target="rwyUPL19n1b9p3f1DsXw-26">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="rwyUPL19n1b9p3f1DsXw-35" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;exitX=0;exitY=0.75;exitDx=0;exitDy=0;entryX=1;entryY=0.5;entryDx=0;entryDy=0;" edge="1" parent="1" source="rwyUPL19n1b9p3f1DsXw-25" target="rwyUPL19n1b9p3f1DsXw-33">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="rwyUPL19n1b9p3f1DsXw-25" value="CoderAgent" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#ffe6cc;strokeColor=#d79b00;" vertex="1" parent="1">
          <mxGeometry x="260" y="620" width="120" height="60" as="geometry" />
        </mxCell>
        <mxCell id="rwyUPL19n1b9p3f1DsXw-28" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;exitX=0;exitY=0.75;exitDx=0;exitDy=0;entryX=1;entryY=0.75;entryDx=0;entryDy=0;" edge="1" parent="1" source="rwyUPL19n1b9p3f1DsXw-26" target="rwyUPL19n1b9p3f1DsXw-25">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="rwyUPL19n1b9p3f1DsXw-26" value="ReviewerAgent" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#d5e8d4;strokeColor=#82b366;" vertex="1" parent="1">
          <mxGeometry x="480" y="620" width="120" height="60" as="geometry" />
        </mxCell>
        <mxCell id="rwyUPL19n1b9p3f1DsXw-29" value="CodeReviewTask" style="text;html=1;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;" vertex="1" parent="1">
          <mxGeometry x="395" y="600" width="60" height="30" as="geometry" />
        </mxCell>
        <mxCell id="rwyUPL19n1b9p3f1DsXw-30" value="CodeReviewResult" style="text;html=1;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;" vertex="1" parent="1">
          <mxGeometry x="395" y="677" width="60" height="30" as="geometry" />
        </mxCell>
        <mxCell id="rwyUPL19n1b9p3f1DsXw-32" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;entryX=0;entryY=0.25;entryDx=0;entryDy=0;" edge="1" parent="1" source="rwyUPL19n1b9p3f1DsXw-31" target="rwyUPL19n1b9p3f1DsXw-25">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="rwyUPL19n1b9p3f1DsXw-31" value="CodeWritingTask" style="text;html=1;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;" vertex="1" parent="1">
          <mxGeometry x="100" y="620" width="100" height="30" as="geometry" />
        </mxCell>
        <mxCell id="rwyUPL19n1b9p3f1DsXw-33" value="CodeWritingResult" style="text;html=1;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;" vertex="1" parent="1">
          <mxGeometry x="90" y="650" width="110" height="30" as="geometry" />
        </mxCell>
        <mxCell id="rwyUPL19n1b9p3f1DsXw-36" value="approved=True" style="text;html=1;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;" vertex="1" parent="1">
          <mxGeometry x="220" y="677" width="60" height="30" as="geometry" />
        </mxCell>
      </root>
    </mxGraphModel>
  </diagram>
</mxfile>
