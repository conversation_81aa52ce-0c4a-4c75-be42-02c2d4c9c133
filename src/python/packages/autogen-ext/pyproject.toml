[build-system]
requires = ["hatchling"]
build-backend = "hatchling.build"

[project]
name = "autogen-ext"
version = "0.4.4"
license = {file = "LICENSE-CODE"}
description = "AutoGen extensions library"
readme = "README.md"
requires-python = ">=3.10"
classifiers = [
    "Programming Language :: Python :: 3",
    "License :: OSI Approved :: MIT License",
    "Operating System :: OS Independent",
]
dependencies = [
    "autogen-core==0.4.4",
]

[project.optional-dependencies]
langchain = ["langchain_core~= 0.3.3"]
azure = [
    "azure-ai-inference>=1.0.0b7",
    "azure-core",
    "azure-identity",
]
docker = ["docker~=7.0"]
openai = ["openai>=1.52.2", "tiktoken>=0.8.0", "aiofiles"]
file-surfer = [
    "autogen-agentchat==0.4.4",
    "markitdown>=0.0.1a2",
]
graphrag = ["graphrag>=1.0.1"]
web-surfer = [
    "autogen-agentchat==0.4.4",
    "playwright>=1.48.0",
    "pillow>=11.0.0",
    "markitdown>=0.0.1a2",
]
magentic-one = [
    "autogen-agentchat==0.4.4",
    "markitdown>=0.0.1a2",
    "playwright>=1.48.0",
    "pillow>=11.0.0",
]
video-surfer = [
    "autogen-agentchat==0.4.4",
    "opencv-python>=4.5",
    "ffmpeg-python",
    "openai-whisper",
]
diskcache = [
    "diskcache>=5.6.3"
]
redis = [
    "redis>=5.2.1"
]

grpc = [
    "grpcio~=1.62.0", # TODO: update this once we have a stable version.
]

jupyter-executor = [
    "ipykernel>=6.29.5",
    "nbclient>=0.10.2",
]

semantic-kernel-core = [
    "semantic-kernel>=1.17.1",
]

semantic-kernel-google = [
    "semantic-kernel[google]>=1.17.1",
]

semantic-kernel-hugging-face = [
    "semantic-kernel[hugging_face]>=1.17.1",
]

semantic-kernel-mistralai = [
    "semantic-kernel[mistralai]>=1.17.1",
]

semantic-kernel-ollama = [
    "semantic-kernel[ollama]>=1.17.1",
]

semantic-kernel-onnx = [
    "semantic-kernel[onnx]>=1.17.1",
]

semantic-kernel-anthropic = [
    "semantic-kernel[anthropic]>=1.17.1",
]

semantic-kernel-pandas = [
    "semantic-kernel[pandas]>=1.17.1",
]

semantic-kernel-aws = [
    "semantic-kernel[aws]>=1.17.1",
]

semantic-kernel-dapr = [
    "semantic-kernel[dapr]>=1.17.1",
]

semantic-kernel-all = [
    "semantic-kernel[google,hugging_face,mistralai,ollama,onnx,anthropic,usearch,pandas,aws,dapr]>=1.17.1",
]

rich = ["rich>=13.9.4"]

[tool.hatch.build.targets.wheel]
packages = ["src/autogen_ext"]

[dependency-groups]
dev = [
    "autogen_test_utils",
    "langchain-experimental",
    "pandas-stubs>=2.2.3.241126",
]

[tool.ruff]
extend = "../../pyproject.toml"
include = ["src/**", "tests/*.py"]
exclude = ["src/autogen_ext/agents/web_surfer/*.js", "src/autogen_ext/runtimes/grpc/protos", "tests/protos"]

[tool.pyright]
extends = "../../pyproject.toml"
include = ["src", "tests"]
exclude = ["src/autogen_ext/runtimes/grpc/protos", "tests/protos"]

[tool.pytest.ini_options]
minversion = "6.0"
testpaths = ["tests"]

[tool.poe]
include = "../../shared_tasks.toml"

[tool.poe.tasks]
test.sequence = [
    "playwright install",
    "pytest -n 1 --cov=src --cov-report=term-missing --cov-report=xml",
]
test.default_item_type = "cmd"
mypy = "mypy --config-file ../../pyproject.toml --exclude src/autogen_ext/runtimes/grpc/protos --exclude tests/protos src tests"

[tool.mypy]
[[tool.mypy.overrides]]
module = "docker.*"
ignore_missing_imports = true
