{"name": "weather_team", "component_type": "team", "participants": [{"name": "writing_agent", "component_type": "agent", "model_client": {"model": "gpt-4o-2024-08-06", "model_type": "OpenAIChatCompletionClient", "component_type": "model"}, "tools": [{"name": "get_weather", "description": "Get the weather for a city", "content": "async def get_weather(city: str) -> str:\n    return f\"The weather in {city} is 73 degrees and Sunny.\"", "tool_type": "PythonFunction", "component_type": "tool"}], "agent_type": "AssistantAgent"}], "termination_condition": {"termination_type": "MaxMessageTermination", "max_messages": 5, "component_type": "termination"}, "team_type": "RoundRobinGroupChat", "model_client": null}