[build-system]
requires = ["hatchling"]
build-backend = "hatchling.build"

[project]
name = "autogen-agentchat"
version = "0.4.4"
license = {file = "LICENSE-CODE"}
description = "AutoGen agents and teams library"
readme = "README.md"
requires-python = ">=3.10"
classifiers = [
    "Programming Language :: Python :: 3",
    "License :: OSI Approved :: MIT License",
    "Operating System :: OS Independent",
]
dependencies = [
    "autogen-core==0.4.4",
]

[tool.ruff]
extend = "../../pyproject.toml"
include = ["src/**", "tests/*.py"]

[tool.pyright]
extends = "../../pyproject.toml"
include = ["src", "tests"]
reportDeprecated = true

[tool.pytest.ini_options]
minversion = "6.0"
testpaths = ["tests"]

[tool.poe]
include = "../../shared_tasks.toml"

[tool.poe.tasks]
test = "pytest -n auto --cov=src --cov-report=term-missing --cov-report=xml"
